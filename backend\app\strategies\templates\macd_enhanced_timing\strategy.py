"""
MACD增强择时策略
基于MACD指标的多维度分析择时策略，包含柱状图分析、背离检测和趋势确认
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class MACDEnhancedTiming(UnifiedStrategyCard):
    """MACD增强择时策略：多维度MACD分析包含交叉、柱状图和背离"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 获取策略参数
            params = context.parameters
            fast_period = params.get("fast_period", 12)
            slow_period = params.get("slow_period", 26)
            signal_period = params.get("signal_period", 9)
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 计算MACD指标
                df = self._calculate_macd(df, fast_period, slow_period, signal_period)
                
                # 分析MACD柱状图
                df = self._analyze_macd_histogram(df, params)
                
                # 识别MACD背离
                df = self._identify_macd_divergence(df, params)
                
                # 生成综合信号
                df = self._generate_macd_signals(df, params)
                
                # 打印最近的MACD数据用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条MACD数据:")
                    recent_data = df.tail(last_rows).copy()
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    debug_cols = ['formatted_time', 'close', 'macd', 'signal_line', 'histogram', 'macd_trend', 'signal_type']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的MACD数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_macd(self, df: pd.DataFrame, fast_period: int, slow_period: int, signal_period: int) -> pd.DataFrame:
        """计算MACD指标"""
        try:
            # 计算快速和慢速EMA
            df['ema_fast'] = df['close'].ewm(span=fast_period).mean()
            df['ema_slow'] = df['close'].ewm(span=slow_period).mean()
            
            # 计算MACD线
            df['macd'] = df['ema_fast'] - df['ema_slow']
            
            # 计算信号线（MACD的EMA）
            df['signal_line'] = df['macd'].ewm(span=signal_period).mean()
            
            # 计算MACD柱状图
            df['histogram'] = df['macd'] - df['signal_line']
            
            # MACD趋势方向
            df['macd_trend'] = np.where(df['macd'] > df['macd'].shift(1), 'up', 
                                       np.where(df['macd'] < df['macd'].shift(1), 'down', 'flat'))
            
            return df
            
        except Exception as e:
            self._log(f"计算MACD失败: {str(e)}", "error")
            return df
    
    def _analyze_macd_histogram(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """分析MACD柱状图"""
        try:
            histogram_threshold = float(params.get("histogram_threshold", 0.01))
            
            df['histogram_increasing'] = df['histogram'] > df['histogram'].shift(1)
            df['histogram_decreasing'] = df['histogram'] < df['histogram'].shift(1)
            df['histogram_strength'] = df['histogram'].abs()
            
            # 柱状图动量
            df['histogram_momentum'] = df['histogram'].diff(3)  # 3期动量
            
            # 柱状图过零点
            df['histogram_cross_zero'] = ((df['histogram'].shift(1) <= 0) & (df['histogram'] > 0)) | \
                                        ((df['histogram'].shift(1) >= 0) & (df['histogram'] < 0))
            
            # 柱状图背离预警
            df['histogram_weakening'] = (df['macd'] > df['macd'].shift(3)) & \
                                       (df['histogram'] < df['histogram'].shift(3))
            
            return df
            
        except Exception as e:
            self._log(f"分析MACD柱状图失败: {str(e)}", "error")
            return df
    
    def _identify_macd_divergence(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """识别MACD背离"""
        try:
            lookback_periods = int(params.get("divergence_lookback", 20))
            
            df['bullish_divergence'] = False
            df['bearish_divergence'] = False
            df['divergence_strength'] = 0.0
            
            # 寻找摆动高点和低点
            for i in range(lookback_periods, len(df) - lookback_periods):
                # 价格低点
                price_window = df.iloc[i-lookback_periods:i+lookback_periods+1]['low']
                if df.iloc[i]['low'] == price_window.min():
                    # 检查MACD是否创造更高的低点
                    macd_window = df.iloc[i-lookback_periods:i+lookback_periods+1]['macd']
                    recent_macd_lows = []
                    
                    # 找到最近的MACD低点
                    for j in range(max(0, i-lookback_periods*2), i):
                        local_window = df.iloc[max(0, j-5):min(len(df), j+6)]['macd']
                        if len(local_window) > 0 and df.iloc[j]['macd'] == local_window.min():
                            recent_macd_lows.append((j, df.iloc[j]['macd']))
                    
                    # 检查看涨背离
                    if len(recent_macd_lows) > 0:
                        latest_macd_low = recent_macd_lows[-1]
                        if (df.iloc[i]['low'] < df.iloc[latest_macd_low[0]]['low'] and 
                            df.iloc[i]['macd'] > latest_macd_low[1]):
                            df.iloc[i, df.columns.get_loc('bullish_divergence')] = True
                            strength = (df.iloc[i]['macd'] - latest_macd_low[1]) * 100
                            df.iloc[i, df.columns.get_loc('divergence_strength')] = strength
                
                # 价格高点
                price_window = df.iloc[i-lookback_periods:i+lookback_periods+1]['high']
                if df.iloc[i]['high'] == price_window.max():
                    # 检查MACD是否创造更低的高点
                    recent_macd_highs = []
                    
                    # 找到最近的MACD高点
                    for j in range(max(0, i-lookback_periods*2), i):
                        local_window = df.iloc[max(0, j-5):min(len(df), j+6)]['macd']
                        if len(local_window) > 0 and df.iloc[j]['macd'] == local_window.max():
                            recent_macd_highs.append((j, df.iloc[j]['macd']))
                    
                    # 检查看跌背离
                    if len(recent_macd_highs) > 0:
                        latest_macd_high = recent_macd_highs[-1]
                        if (df.iloc[i]['high'] > df.iloc[latest_macd_high[0]]['high'] and 
                            df.iloc[i]['macd'] < latest_macd_high[1]):
                            df.iloc[i, df.columns.get_loc('bearish_divergence')] = True
                            strength = (latest_macd_high[1] - df.iloc[i]['macd']) * 100
                            df.iloc[i, df.columns.get_loc('divergence_strength')] = strength
            
            return df
            
        except Exception as e:
            self._log(f"识别MACD背离失败: {str(e)}", "error")
            return df
    
    def _generate_macd_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成MACD综合信号"""
        try:
            min_histogram_strength = float(params.get("min_histogram_strength", 0.005))
            
            df['signal_type'] = 'HOLD'
            df['signal_strength'] = 0.0
            df['signal_reason'] = 'no_signal'
            
            for i in range(1, len(df)):
                if pd.isna(df.iloc[i]['macd']) or pd.isna(df.iloc[i-1]['macd']):
                    continue
                
                current = df.iloc[i]
                previous = df.iloc[i-1]
                
                # MACD金叉
                if (previous['macd'] <= previous['signal_line'] and 
                    current['macd'] > current['signal_line'] and
                    current['histogram_strength'] >= min_histogram_strength):
                    
                    strength = 0.6
                    reason = 'macd_golden_cross'
                    
                    # 背离加强信号
                    if current['bullish_divergence']:
                        strength += 0.2
                        reason = 'macd_golden_cross_with_divergence'
                    
                    # 柱状图动量确认
                    if current['histogram_increasing'] and current['histogram_momentum'] > 0:
                        strength += 0.1
                    
                    # 零轴以下金叉更有效
                    if current['macd'] < 0:
                        strength += 0.1
                    
                    df.iloc[i, df.columns.get_loc('signal_type')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = min(0.95, strength)
                    df.iloc[i, df.columns.get_loc('signal_reason')] = reason
                
                # MACD死叉
                elif (previous['macd'] >= previous['signal_line'] and 
                      current['macd'] < current['signal_line'] and
                      current['histogram_strength'] >= min_histogram_strength):
                    
                    strength = 0.6
                    reason = 'macd_death_cross'
                    
                    # 背离加强信号
                    if current['bearish_divergence']:
                        strength += 0.2
                        reason = 'macd_death_cross_with_divergence'
                    
                    # 柱状图动量确认
                    if current['histogram_decreasing'] and current['histogram_momentum'] < 0:
                        strength += 0.1
                    
                    # 零轴以上死叉更有效
                    if current['macd'] > 0:
                        strength += 0.1
                    
                    df.iloc[i, df.columns.get_loc('signal_type')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = min(0.95, strength)
                    df.iloc[i, df.columns.get_loc('signal_reason')] = reason
                
                # 柱状图转向信号
                elif current['histogram_cross_zero']:
                    if current['histogram'] > 0 and current['histogram_increasing']:
                        df.iloc[i, df.columns.get_loc('signal_type')] = 'BUY'
                        df.iloc[i, df.columns.get_loc('signal_strength')] = 0.4
                        df.iloc[i, df.columns.get_loc('signal_reason')] = 'histogram_positive_cross'
                    elif current['histogram'] < 0 and current['histogram_decreasing']:
                        df.iloc[i, df.columns.get_loc('signal_type')] = 'SELL'
                        df.iloc[i, df.columns.get_loc('signal_strength')] = 0.4
                        df.iloc[i, df.columns.get_loc('signal_reason')] = 'histogram_negative_cross'
                
                # 背离独立信号
                elif current['bullish_divergence']:
                    df.iloc[i, df.columns.get_loc('signal_type')] = 'BUY'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.5 + min(0.3, current['divergence_strength']/100)
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'macd_bullish_divergence'
                    
                elif current['bearish_divergence']:
                    df.iloc[i, df.columns.get_loc('signal_type')] = 'SELL'
                    df.iloc[i, df.columns.get_loc('signal_strength')] = 0.5 + min(0.3, current['divergence_strength']/100)
                    df.iloc[i, df.columns.get_loc('signal_reason')] = 'macd_bearish_divergence'
            
            return df
            
        except Exception as e:
            self._log(f"生成MACD信号失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < 30:
                    self._log(f"股票{symbol}数据不足，无法生成MACD信号")
                    continue
                
                # 获取最近的交易信号
                recent_signals = df[df['signal_type'] != 'HOLD'].tail(5)
                
                # 获取当前MACD状态
                latest = df.iloc[-1]
                current_price = latest['close']
                current_macd = latest['macd']
                current_signal = latest['signal_line']
                current_histogram = latest['histogram']
                
                if not recent_signals.empty:
                    # 处理交易信号
                    for _, row in recent_signals.iterrows():
                        direction = row['signal_type']
                        confidence = row['signal_strength']
                        reason = row['signal_reason']
                        
                        # 构建触发条件描述
                        reason_map = {
                            'macd_golden_cross': 'MACD金叉',
                            'macd_death_cross': 'MACD死叉',
                            'macd_golden_cross_with_divergence': 'MACD金叉+看涨背离',
                            'macd_death_cross_with_divergence': 'MACD死叉+看跌背离',
                            'histogram_positive_cross': 'MACD柱状图转正',
                            'histogram_negative_cross': 'MACD柱状图转负',
                            'macd_bullish_divergence': 'MACD看涨背离',
                            'macd_bearish_divergence': 'MACD看跌背离'
                        }
                        condition = reason_map.get(reason, 'MACD信号')
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=direction,
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                macd_value=float(row['macd']),
                                signal_line=float(row['signal_line']),
                                histogram=float(row['histogram']),
                                macd_trend=str(row['macd_trend']),
                                divergence_strength=float(row.get('divergence_strength', 0))
                            )
                        )
                else:
                    # 生成状态信号
                    if pd.notna(current_macd) and pd.notna(current_signal):
                        if current_macd > current_signal:
                            if current_macd > 0:
                                condition = f"MACD多头强势_MACD{current_macd:.4f}"
                                confidence = 0.4
                            else:
                                condition = f"MACD多头整理_MACD{current_macd:.4f}"
                                confidence = 0.3
                        else:
                            if current_macd < 0:
                                condition = f"MACD空头弱势_MACD{current_macd:.4f}"
                                confidence = 0.4
                            else:
                                condition = f"MACD空头整理_MACD{current_macd:.4f}"
                                confidence = 0.3
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="HOLD",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                macd_value=float(current_macd),
                                signal_line=float(current_signal),
                                histogram=float(current_histogram) if pd.notna(current_histogram) else 0
                            )
                        )
            
            self._log(f"共生成{len(signals)}个MACD信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行MACD增强择时策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"MACD策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []