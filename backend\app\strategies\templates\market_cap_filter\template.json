{"template_id": "market_cap_filter", "name": "市值", "description": "根据股票市值进行筛选   <span style='color: #808080'>例：市值小于500亿元。</span>", "version": "1.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "市值比较条件", "required": true, "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}], "default": "大于"}, "market_cap1": {"type": "number", "label": "市值条件1", "description": "市值条件1（亿元）", "required": true, "default": 100, "min": 0, "max": 100000, "unit": "亿元"}, "market_cap2": {"type": "number", "label": "市值条件2", "description": "市值条件2（亿元），仅在'区间'模式下使用", "required": false, "default": null, "min": 0, "max": 100000, "unit": "亿元", "visibleWhen": {"parameter": "operator", "value": "区间"}}}, "parameterGroups": {"market_cap_filter": {"parameters": ["operator", "market_cap1", "market_cap2"], "displayMode": "inline", "prefix": "市值", "separator": "-", "layout": "horizontal"}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"代码": {"type": "string"}, "名称": {"type": "string"}, "总市值": {"type": "number"}}}}}, "ui": {"icon": "filter", "color": "#1890ff", "group": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}