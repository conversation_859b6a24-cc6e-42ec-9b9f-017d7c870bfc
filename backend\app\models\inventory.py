"""
用户库存管理模型
支持策略卡牌库存、消耗记录、获取来源追踪
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId
from app.core.base_model import BaseDBModel

class InventoryItem(BaseDBModel):
    """用户库存物品模型"""
    user_id: str = Field(..., description="用户ID")
    template_id: str = Field(..., description="策略模板ID")
    quantity: int = Field(default=0, description="当前数量")
    total_acquired: int = Field(default=0, description="累计获得数量")
    source: str = Field(..., description="获取来源: activity|purchase|reward|achievement|gift")
    acquired_at: datetime = Field(default_factory=datetime.utcnow, description="首次获取时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="最后更新时间")
    
    # 扩展属性
    rarity: str = Field(default="common", description="稀有度: common|rare|epic|legendary|mythic")
    level: int = Field(default=1, description="卡牌等级")
    experience: int = Field(default=0, description="卡牌经验值")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "user_inventories"

    @classmethod
    async def get_by_user_and_template(cls, user_id: str, template_id: str) -> Optional["InventoryItem"]:
        """获取用户特定模板的库存"""
        return await cls.find_one({"user_id": user_id, "template_id": template_id})

    @classmethod
    async def get_user_inventory(cls, user_id: str, available_only: bool = False) -> List["InventoryItem"]:
        """获取用户完整库存"""
        filter_dict = {"user_id": user_id}
        if available_only:
            filter_dict["quantity"] = {"$gt": 0}
        
        docs = await cls.find_many(filter_dict, sort=[("updated_at", -1)])
        return docs

    async def add_quantity(self, amount: int, source: str = None, transaction_id: str = None) -> "InventoryItem":
        """增加库存数量"""
        self.quantity += amount
        self.total_acquired += amount
        self.updated_at = datetime.utcnow()
        
        # 记录交易日志
        if transaction_id:
            await InventoryTransaction.create_transaction({
                "user_id": self.user_id,
                "template_id": self.template_id,
                "type": "add",
                "delta": amount,
                "source": source or self.source,
                "reason": f"添加 {amount} 个 {self.template_id}",
                "transaction_id": transaction_id,
                "before_quantity": self.quantity - amount,
                "after_quantity": self.quantity,
                "metadata": {"batch_add": True}
            })
            
        await self.save()
        return self

    async def consume_quantity(self, amount: int, reason: str = "strategy_execution", transaction_id: str = None) -> bool:
        """消耗库存数量"""
        if self.quantity < amount:
            return False
            
        before_quantity = self.quantity
        self.quantity -= amount
        self.updated_at = datetime.utcnow()
        
        # 记录交易日志
        if transaction_id:
            await InventoryTransaction.create_transaction({
                "user_id": self.user_id,
                "template_id": self.template_id,
                "type": "consume",
                "delta": -amount,
                "source": "consumption",
                "reason": reason,
                "transaction_id": transaction_id,
                "before_quantity": before_quantity,
                "after_quantity": self.quantity,
                "metadata": {"consumption_reason": reason}
            })
            
        await self.save()
        return True

class InventoryTransaction(BaseDBModel):
    """库存交易日志模型 - 审计追踪"""
    user_id: str = Field(..., description="用户ID")
    template_id: str = Field(..., description="策略模板ID")
    type: str = Field(..., description="操作类型: add|consume|adjust|transfer")
    delta: int = Field(..., description="变化量（正负）")
    source: str = Field(..., description="来源类型")
    reason: str = Field(..., description="操作原因")
    transaction_id: Optional[str] = Field(None, description="事务ID（幂等性）")
    before_quantity: int = Field(..., description="操作前数量")
    after_quantity: int = Field(..., description="操作后数量")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="记录时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="扩展信息")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "inventory_transactions"

    @classmethod
    async def create_transaction(cls, transaction_data: Dict[str, Any]) -> "InventoryTransaction":
        """创建交易记录"""
        transaction = cls(**transaction_data)
        await transaction.save()
        return transaction

    @classmethod
    async def get_user_transactions(cls, user_id: str, template_id: str = None, limit: int = 50) -> List["InventoryTransaction"]:
        """获取用户交易历史"""
        filter_dict = {"user_id": user_id}
        if template_id:
            filter_dict["template_id"] = template_id
            
        return await cls.find_many(
            filter_dict,
            sort=[("created_at", -1)],
            limit=limit
        )

class GameSession(BaseDBModel):
    """游戏会话状态模型"""
    user_id: str = Field(..., description="用户ID")
    session_type: str = Field(..., description="会话类型: battle|adventure|strategy_creation")
    session_data: Dict[str, Any] = Field(default_factory=dict, description="会话数据")
    participants: List[str] = Field(default_factory=list, description="多人场景参与者")
    status: str = Field(default="active", description="会话状态: active|paused|completed|expired")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="更新时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def get_collection_name(cls) -> str:
        return "game_sessions"

    @classmethod
    async def get_active_session(cls, user_id: str, session_type: str = None) -> Optional["GameSession"]:
        """获取用户活跃会话"""
        filter_dict = {"user_id": user_id, "status": "active"}
        if session_type:
            filter_dict["session_type"] = session_type
            
        return await cls.find_one(filter_dict)

    async def update_session_data(self, data: Dict[str, Any]) -> "GameSession":
        """更新会话数据"""
        self.session_data.update(data)
        self.updated_at = datetime.utcnow()
        await self.save()
        return self

    async def expire_session(self) -> "GameSession":
        """使会话过期"""
        self.status = "expired"
        self.updated_at = datetime.utcnow()
        await self.save()
        return self

# 库存管理服务类
class InventoryManager:
    """库存管理器 - 业务逻辑封装"""
    
    @staticmethod
    async def add_cards_to_user(user_id: str, template_id: str, quantity: int, 
                               source: str = "activity", transaction_id: str = None) -> InventoryItem:
        """为用户添加卡牌"""
        # 查找现有库存
        inventory = await InventoryItem.get_by_user_and_template(user_id, template_id)
        
        if inventory:
            # 增加现有库存
            await inventory.add_quantity(quantity, source, transaction_id)
        else:
            # 创建新库存记录
            inventory = InventoryItem(
                user_id=user_id,
                template_id=template_id,
                quantity=quantity,
                total_acquired=quantity,
                source=source
            )
            await inventory.save()
            
            # 记录初始交易
            if transaction_id:
                await InventoryTransaction.create_transaction({
                    "user_id": user_id,
                    "template_id": template_id,
                    "type": "add",
                    "delta": quantity,
                    "source": source,
                    "reason": f"首次获得 {quantity} 个 {template_id}",
                    "transaction_id": transaction_id,
                    "before_quantity": 0,
                    "after_quantity": quantity,
                    "metadata": {"initial_grant": True}
                })
        
        return inventory

    @staticmethod
    async def consume_cards_from_user(user_id: str, template_id: str, quantity: int, 
                                    reason: str = "strategy_execution", 
                                    transaction_id: str = None) -> bool:
        """从用户库存消耗卡牌"""
        inventory = await InventoryItem.get_by_user_and_template(user_id, template_id)
        
        if not inventory or inventory.quantity < quantity:
            return False
            
        return await inventory.consume_quantity(quantity, reason, transaction_id)

    @staticmethod
    async def get_user_inventory_summary(user_id: str) -> Dict[str, Any]:
        """获取用户库存摘要"""
        inventory_items = await InventoryItem.get_user_inventory(user_id)
        
        total_cards = sum(item.quantity for item in inventory_items)
        total_types = len(inventory_items)
        available_types = len([item for item in inventory_items if item.quantity > 0])
        
        rarity_breakdown = {}
        for item in inventory_items:
            rarity = item.rarity
            if rarity not in rarity_breakdown:
                rarity_breakdown[rarity] = {"count": 0, "quantity": 0}
            rarity_breakdown[rarity]["count"] += 1
            rarity_breakdown[rarity]["quantity"] += item.quantity

        return {
            "total_cards": total_cards,
            "total_types": total_types,
            "available_types": available_types,
            "rarity_breakdown": rarity_breakdown,
            "items": [item.dict() for item in inventory_items]
        }

    @staticmethod
    async def transfer_cards(from_user_id: str, to_user_id: str, template_id: str, 
                           quantity: int, transaction_id: str = None) -> bool:
        """用户间卡牌转移"""
        # 从源用户消耗
        consumed = await InventoryManager.consume_cards_from_user(
            from_user_id, template_id, quantity, "transfer_out", transaction_id
        )
        
        if not consumed:
            return False
            
        # 给目标用户添加
        await InventoryManager.add_cards_to_user(
            to_user_id, template_id, quantity, "transfer_in", transaction_id
        )
        
        return True

    @staticmethod
    async def grant_starter_pack(user_id: str) -> Dict[str, Any]:
        """为新用户发放启动卡包"""
        import uuid
        transaction_id = str(uuid.uuid4())
        
        # 启动卡包配置 - 基于数据库中的策略模板
        starter_cards = [
            {"template_id": "67ef849d0fd46c6c0c15fc1a", "quantity": 3, "rarity": "common"},  # 涨幅
            {"template_id": "67ef849d0fd46c6c0c15fc19", "quantity": 2, "rarity": "common"},  # 振幅
            {"template_id": "67ef849d0fd46c6c0c15fc24", "quantity": 2, "rarity": "common"},  # 换手率
            {"template_id": "67ef849d0fd46c6c0c15fc1c", "quantity": 1, "rarity": "rare"},    # 价格筛选
            {"template_id": "67ef849d0fd46c6c0c15fc1f", "quantity": 1, "rarity": "rare"},    # 市值筛选
        ]
        
        granted_cards = []
        for card_config in starter_cards:
            try:
                # 创建库存记录
                inventory = InventoryItem(
                    user_id=user_id,
                    template_id=card_config["template_id"],
                    quantity=card_config["quantity"],
                    total_acquired=card_config["quantity"],
                    source="starter_pack",
                    rarity=card_config["rarity"]
                )
                await inventory.save()
                
                # 记录交易
                await InventoryTransaction.create_transaction({
                    "user_id": user_id,
                    "template_id": card_config["template_id"],
                    "type": "add",
                    "delta": card_config["quantity"],
                    "source": "starter_pack",
                    "reason": "新用户启动卡包",
                    "transaction_id": transaction_id,
                    "before_quantity": 0,
                    "after_quantity": card_config["quantity"],
                    "metadata": {"starter_pack": True, "rarity": card_config["rarity"]}
                })
                
                granted_cards.append({
                    "template_id": card_config["template_id"],
                    "quantity": card_config["quantity"],
                    "rarity": card_config["rarity"]
                })
                
            except Exception as e:
                print(f"发放启动卡包失败 {card_config['template_id']}: {e}")
                continue
        
        return {
            "success": True,
            "message": f"成功发放启动卡包，共 {len(granted_cards)} 种卡牌",
            "cards": granted_cards,
            "transaction_id": transaction_id
        } 