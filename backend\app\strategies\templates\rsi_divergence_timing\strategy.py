"""
RSI背离择时策略
基于RSI与价格的背离分析进行择时交易
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class RSIDivergenceTiming(UnifiedStrategyCard):
    """RSI背离择时策略：识别RSI与价格背离产生的交易机会"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 获取策略参数
            params = context.parameters
            rsi_period = params.get("rsi_period", 14)
            lookback_window = params.get("lookback_window", 20)
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 计算RSI
                df['rsi'] = self._calculate_rsi(df['close'], rsi_period)
                
                # 寻找高点和低点
                df = self._find_swing_points(df, lookback_window)
                
                # 识别背离
                df = self._identify_divergences(df, params)
                
                # 打印最近的RSI和背离数据用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条RSI数据:")
                    recent_data = df.tail(last_rows).copy()
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    debug_cols = ['formatted_time', 'close', 'rsi', 'is_swing_high', 'is_swing_low', 'bullish_divergence', 'bearish_divergence']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的RSI数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception as e:
            self._log(f"计算RSI失败: {str(e)}", "error")
            return pd.Series([50] * len(prices))
    
    def _find_swing_points(self, df: pd.DataFrame, lookback: int) -> pd.DataFrame:
        """寻找价格和RSI的摆动高点和低点"""
        try:
            df['is_swing_high'] = False
            df['is_swing_low'] = False
            df['rsi_swing_high'] = False
            df['rsi_swing_low'] = False
            
            # 寻找价格摆动点
            for i in range(lookback, len(df) - lookback):
                # 价格摆动高点
                if df.iloc[i]['high'] == df.iloc[i-lookback:i+lookback+1]['high'].max():
                    df.iloc[i, df.columns.get_loc('is_swing_high')] = True
                
                # 价格摆动低点
                if df.iloc[i]['low'] == df.iloc[i-lookback:i+lookback+1]['low'].min():
                    df.iloc[i, df.columns.get_loc('is_swing_low')] = True
                
                # RSI摆动高点
                if pd.notna(df.iloc[i]['rsi']) and df.iloc[i]['rsi'] == df.iloc[i-lookback:i+lookback+1]['rsi'].max():
                    df.iloc[i, df.columns.get_loc('rsi_swing_high')] = True
                
                # RSI摆动低点  
                if pd.notna(df.iloc[i]['rsi']) and df.iloc[i]['rsi'] == df.iloc[i-lookback:i+lookback+1]['rsi'].min():
                    df.iloc[i, df.columns.get_loc('rsi_swing_low')] = True
            
            return df
            
        except Exception as e:
            self._log(f"寻找摆动点失败: {str(e)}", "error")
            return df
    
    def _identify_divergences(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """识别RSI与价格的背离"""
        try:
            min_divergence_strength = float(params.get("min_divergence_strength", 5))
            max_divergence_distance = int(params.get("max_divergence_distance", 50))
            
            df['bullish_divergence'] = False
            df['bearish_divergence'] = False
            df['divergence_strength'] = 0.0
            
            # 获取摆动点
            swing_highs = df[df['is_swing_high'] == True].copy()
            swing_lows = df[df['is_swing_low'] == True].copy()
            rsi_swing_highs = df[df['rsi_swing_high'] == True].copy()
            rsi_swing_lows = df[df['rsi_swing_low'] == True].copy()
            
            # 检测看涨背离（价格创新低，RSI不创新低）
            for idx in swing_lows.index:
                # 寻找之前的摆动低点
                previous_lows = swing_lows[swing_lows.index < idx]
                if len(previous_lows) == 0:
                    continue
                    
                for prev_idx in previous_lows.index[-3:]:  # 检查最近3个摆动低点
                    distance = idx - prev_idx
                    if distance > max_divergence_distance:
                        continue
                        
                    # 价格创新低
                    current_low = df.loc[idx, 'low']
                    previous_low = df.loc[prev_idx, 'low']
                    
                    # RSI相应位置的值
                    current_rsi = df.loc[idx, 'rsi']
                    previous_rsi = df.loc[prev_idx, 'rsi']
                    
                    if (pd.notna(current_rsi) and pd.notna(previous_rsi) and 
                        current_low < previous_low and current_rsi > previous_rsi):
                        
                        # 计算背离强度
                        price_change = (current_low - previous_low) / previous_low * 100
                        rsi_change = current_rsi - previous_rsi
                        strength = abs(price_change) + rsi_change
                        
                        if strength >= min_divergence_strength:
                            df.loc[idx, 'bullish_divergence'] = True
                            df.loc[idx, 'divergence_strength'] = strength
                            break
            
            # 检测看跌背离（价格创新高，RSI不创新高）
            for idx in swing_highs.index:
                # 寻找之前的摆动高点
                previous_highs = swing_highs[swing_highs.index < idx]
                if len(previous_highs) == 0:
                    continue
                    
                for prev_idx in previous_highs.index[-3:]:  # 检查最近3个摆动高点
                    distance = idx - prev_idx
                    if distance > max_divergence_distance:
                        continue
                        
                    # 价格创新高
                    current_high = df.loc[idx, 'high']
                    previous_high = df.loc[prev_idx, 'high']
                    
                    # RSI相应位置的值
                    current_rsi = df.loc[idx, 'rsi']
                    previous_rsi = df.loc[prev_idx, 'rsi']
                    
                    if (pd.notna(current_rsi) and pd.notna(previous_rsi) and 
                        current_high > previous_high and current_rsi < previous_rsi):
                        
                        # 计算背离强度
                        price_change = (current_high - previous_high) / previous_high * 100
                        rsi_change = previous_rsi - current_rsi
                        strength = price_change + rsi_change
                        
                        if strength >= min_divergence_strength:
                            df.loc[idx, 'bearish_divergence'] = True
                            df.loc[idx, 'divergence_strength'] = strength
                            break
            
            return df
            
        except Exception as e:
            self._log(f"识别背离失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            # 获取参数
            oversold_level = float(params.get("oversold_level", 30))
            overbought_level = float(params.get("overbought_level", 70))
            confirmation_periods = int(params.get("confirmation_periods", 2))
            
            self._log(f"RSI背离策略参数: 超卖线={oversold_level}, 超买线={overbought_level}, 确认周期={confirmation_periods}")
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < 20:
                    self._log(f"股票{symbol}数据不足，无法生成信号")
                    continue
                
                # 获取最近的背离信号
                recent_bullish = df[df['bullish_divergence'] == True].tail(3)
                recent_bearish = df[df['bearish_divergence'] == True].tail(3)
                
                # 获取当前RSI状态
                latest = df.iloc[-1]
                current_rsi = latest['rsi']
                current_price = latest['close']
                
                # 看涨背离信号
                for _, row in recent_bullish.iterrows():
                    # 检查RSI是否在超卖区域
                    if row['rsi'] <= oversold_level:
                        confidence = min(0.9, 0.6 + (row['divergence_strength'] - 5) / 50)
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="BUY",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=f"RSI看涨背离_RSI{row['rsi']:.1f}",
                                current_price=float(current_price),
                                rsi_value=float(row['rsi']),
                                divergence_strength=float(row['divergence_strength']),
                                swing_low_price=float(row['low'])
                            )
                        )
                
                # 看跌背离信号
                for _, row in recent_bearish.iterrows():
                    # 检查RSI是否在超买区域
                    if row['rsi'] >= overbought_level:
                        confidence = min(0.9, 0.6 + (row['divergence_strength'] - 5) / 50)
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="SELL",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=f"RSI看跌背离_RSI{row['rsi']:.1f}",
                                current_price=float(current_price),
                                rsi_value=float(row['rsi']),
                                divergence_strength=float(row['divergence_strength']),
                                swing_high_price=float(row['high'])
                            )
                        )
                
                # 如果没有背离信号，根据RSI位置生成状态信号
                if len([s for s in signals if s.symbol == symbol]) == 0:
                    if pd.notna(current_rsi):
                        if current_rsi <= oversold_level:
                            condition = f"RSI超卖{current_rsi:.1f}"
                            direction = "HOLD"
                            confidence = 0.4
                        elif current_rsi >= overbought_level:
                            condition = f"RSI超买{current_rsi:.1f}"
                            direction = "HOLD"
                            confidence = 0.4
                        else:
                            condition = f"RSI中性{current_rsi:.1f}"
                            direction = "HOLD"
                            confidence = 0.3
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=direction,
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                rsi_value=float(current_rsi)
                            )
                        )
            
            self._log(f"共生成{len(signals)}个RSI背离信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行RSI背离择时策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"RSI背离策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []