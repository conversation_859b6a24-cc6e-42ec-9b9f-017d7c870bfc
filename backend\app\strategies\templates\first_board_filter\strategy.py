"""
首板策略
检测近期第一个涨停板的股票选股策略
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.daily_kline_service import DailyKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class FirstBoardFilter(UnifiedStrategyCard):
    """首板策略：筛选近期出现第一个涨停板的股票"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化日K线服务
        self.kline_service = DailyKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取日K线数据
            kline_data = context.data_cache.get("daily_kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取日K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df = df.sort_values(by='trade_date').copy()
                
                # 计算涨跌幅
                df = self._calculate_price_change(df)
                
                # 计算平均成交量
                df = self._calculate_volume_metrics(df, context.parameters)
                
                # 检测涨停板
                df = self._detect_limit_up(df, context.parameters)
                
                # 分析首板模式
                df = self._analyze_first_board_pattern(df, context.parameters)
                
                processed_data[symbol] = df
                
                # 打印调试信息
                if len(df) > 0:
                    first_board_count = len(df[df['is_first_board'] == True])
                    if first_board_count > 0:
                        self._log(f"股票{symbol}发现{first_board_count}个首板信号")
                        recent_boards = df[df['is_first_board'] == True].tail(3)
                        for _, row in recent_boards.iterrows():
                            self._log(f"  {row['trade_date']}: 涨幅{row['pct_change']:.2f}%, 成交量倍数{row['volume_ratio']:.2f}")
            
            self._log(f"成功处理{len(processed_data)}只股票的首板数据")
            return processed_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_price_change(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算涨跌幅"""
        try:
            # 使用pre_close计算涨跌幅
            if 'pre_close' in df.columns:
                df['pct_change'] = ((df['close'] - df['pre_close']) / df['pre_close'] * 100).fillna(0)
            else:
                # 如果没有pre_close，使用前一日收盘价
                df['pre_close'] = df['close'].shift(1)
                df['pct_change'] = ((df['close'] - df['pre_close']) / df['pre_close'] * 100).fillna(0)
            
            return df
            
        except Exception as e:
            self._log(f"计算涨跌幅失败: {str(e)}", "error")
            return df
    
    def _calculate_volume_metrics(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算成交量指标"""
        try:
            # 计算5日平均成交量
            df['volume_5ma'] = df['volume'].rolling(window=5, min_periods=1).mean()
            
            # 计算20日平均成交量
            df['volume_20ma'] = df['volume'].rolling(window=20, min_periods=1).mean()
            
            # 计算成交量比率（当日成交量/5日平均成交量）
            df['volume_ratio'] = df['volume'] / df['volume_5ma']
            df['volume_ratio'] = df['volume_ratio'].fillna(1.0)
            
            # 计算成交量相对强度
            df['volume_strength'] = df['volume'] / df['volume_20ma']
            df['volume_strength'] = df['volume_strength'].fillna(1.0)
            
            return df
            
        except Exception as e:
            self._log(f"计算成交量指标失败: {str(e)}", "error")
            return df
    
    def _detect_limit_up(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """检测涨停板"""
        try:
            min_increase_rate = float(params.get("min_increase_rate", 9.8))
            min_volume_ratio = float(params.get("min_volume_ratio", 1.5))
            
            # 涨停板判断条件
            df['is_limit_up'] = (
                (df['pct_change'] >= min_increase_rate) &  # 涨幅达到标准
                (df['volume_ratio'] >= min_volume_ratio) &  # 成交量放大
                (df['volume'] > 0) &  # 有成交量
                (df['high'] == df['close'])  # 以最高价收盘（可选条件）
            )
            
            # 标记强势涨停（以涨停价收盘且成交量大）
            df['is_strong_limit'] = (
                df['is_limit_up'] & 
                (df['pct_change'] >= min_increase_rate + 0.1) &
                (df['volume_ratio'] >= min_volume_ratio + 0.5)
            )
            
            return df
            
        except Exception as e:
            self._log(f"检测涨停板失败: {str(e)}", "error")
            return df
    
    def _analyze_first_board_pattern(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """分析首板模式"""
        try:
            lookback_days = int(params.get("lookback_days", 30))
            
            df['is_first_board'] = False
            df['days_since_last_limit'] = 0
            df['first_board_strength'] = 0.0
            
            # 找出所有涨停板的日期
            limit_up_indices = df[df['is_limit_up']].index.tolist()
            
            for i, idx in enumerate(limit_up_indices):
                current_date = df.loc[idx, 'trade_date']
                
                # 计算当前涨停前lookback_days天内是否有其他涨停
                start_date = current_date - timedelta(days=lookback_days)
                
                # 查找历史涨停
                historical_limits = df[
                    (df['trade_date'] >= start_date) & 
                    (df['trade_date'] < current_date) & 
                    (df['is_limit_up'] == True)
                ]
                
                # 如果历史期间内没有涨停板，则为首板
                if len(historical_limits) == 0:
                    df.loc[idx, 'is_first_board'] = True
                    
                    # 计算首板强度
                    current_row = df.loc[idx]
                    strength = min(1.0, (
                        0.3 * min(1.0, (current_row['pct_change'] - 9.8) / 1.0) +  # 涨幅贡献
                        0.4 * min(1.0, (current_row['volume_ratio'] - 1.5) / 3.0) +  # 成交量贡献
                        0.3 * (1.0 if current_row['is_strong_limit'] else 0.5)  # 强势程度贡献
                    ))
                    df.loc[idx, 'first_board_strength'] = max(0.5, strength)
                
                # 计算距离上次涨停的天数
                if i > 0:
                    prev_limit_date = df.loc[limit_up_indices[i-1], 'trade_date']
                    days_diff = (current_date - prev_limit_date).days
                    df.loc[idx, 'days_since_last_limit'] = days_diff
            
            return df
            
        except Exception as e:
            self._log(f"分析首板模式失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            lookback_days = int(params.get("lookback_days", 30))
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 找出所有首板信号
                first_board_signals = df[df['is_first_board'] == True]
                
                if not first_board_signals.empty:
                    # 获取最近的首板信号
                    for _, row in first_board_signals.tail(3).iterrows():
                        trade_date = row['trade_date']
                        current_price = row['close']
                        pct_change = row['pct_change']
                        volume_ratio = row['volume_ratio']
                        strength = row['first_board_strength']
                        
                        # 构建触发条件描述
                        condition = f"首板_涨幅{pct_change:.2f}%_成交量{volume_ratio:.1f}倍"
                        
                        # 计算置信度
                        confidence = min(0.9, strength)
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="BUY",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                trade_date=trade_date.strftime('%Y-%m-%d'),
                                pct_change=float(pct_change),
                                volume_ratio=float(volume_ratio),
                                is_strong_limit=bool(row['is_strong_limit']),
                                first_board_strength=float(strength),
                                lookback_days=lookback_days
                            )
                        )
                else:
                    # 没有首板信号，但可能有其他涨停信号
                    recent_limits = df[df['is_limit_up'] == True].tail(1)
                    if not recent_limits.empty:
                        row = recent_limits.iloc[-1]
                        days_since_last = row['days_since_last_limit']
                        
                        if days_since_last > 0:
                            condition = f"非首板涨停_距上次{days_since_last}天_涨幅{row['pct_change']:.2f}%"
                            confidence = 0.3
                        else:
                            condition = f"连续涨停_涨幅{row['pct_change']:.2f}%"
                            confidence = 0.4
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="HOLD",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(row['close']),
                                is_first_board=False,
                                days_since_last_limit=int(days_since_last)
                            )
                        )
            
            self._log(f"首板策略共生成{len(signals)}个信号")
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            self._log(f"其中首板买入信号{buy_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行首板选股策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"首板策略执行完成: 首板买入信号{buy_signals}个, 其他信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []