"""
初始化数据
"""
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List

from .data.db.base import db_manager

logger = logging.getLogger(__name__)

async def init_strategy_templates():
    """初始化策略模板数据
    
    从backend/strategies/templates目录中读取策略模板，
    将template.json配置同步到数据库中。
    
    策略目录结构：
    templates/
        strategy_name/
            template.json    # 策略配置文件
            strategy.py      # 策略实现代码
    """
    try:
        # 获取templates目录下的所有策略文件夹
        templates_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "strategies", "templates")
        strategy_folders = [f for f in os.listdir(templates_dir) 
                          if os.path.isdir(os.path.join(templates_dir, f)) and not f.startswith('__')]
        
        logger.info(f"发现策略文件夹: {strategy_folders}")
        
        # 获取数据库连接
        db = await db_manager.get_mongodb_database("quantcard")
        
        for folder in strategy_folders:
            try:
                # 读取template.json
                template_file = os.path.join(templates_dir, folder, "template.json")
                if not os.path.exists(template_file):
                    logger.warning(f"策略文件夹 {folder} 中未找到 template.json")
                    continue
                
                logger.info(f"正在处理策略配置: {template_file}")
                
                # 加载模板配置
                with open(template_file, "r", encoding="utf-8") as f:
                    template_data = json.load(f)
                
                logger.info(f"加载策略配置: {template_data.get('name')}")
                
                # 验证参数格式
                parameters = template_data.get("parameters", {})
                for param_name, param_config in parameters.items():
                    # 确保参数配置包含所有必要字段
                    if "type" not in param_config:
                        logger.warning(f"参数 {param_name} 缺少 type 字段，设置默认值为 'text'")
                        param_config["type"] = "text"
                    
                    if "label" not in param_config:
                        param_config["label"] = param_name
                        
                    if "required" not in param_config:
                        param_config["required"] = False
                        
                    if "default" not in param_config:
                        param_config["default"] = None
                
                # 检查数据库中是否已存在，以template_id为主要查询条件
                existing = await db.strategy_templates.find_one({
                    "$or": [
                        {"template_id": template_data.get("template_id") or template_data.get("type")},  # 优先使用template_id，fallback到type
                        {"name": template_data.get("name")}
                    ]
                })
                
                # 直接使用template.json的数据结构，添加必要的数据库字段
                template_dict = {
                    "name": template_data.get("name"),
                    "description": template_data.get("description", ""),
                    # 移除type字段，统一使用template_id作为唯一标识符
                    "version": template_data.get("version", "1.0.0"),
                    "author": template_data.get("author", "system"),
                    "stars": template_data.get("stars", 3),
                    "tags": template_data.get("tags", []),
                    "parameters": parameters,
                    "parameterGroups": template_data.get("parameterGroups", {}),
                    "ui": template_data.get("ui", {
                        "form": {
                            "layout": "horizontal",
                            "compact": True,
                            "showDescription": False
                        }
                    }),
                    "template_id": template_data.get("template_id") or template_data.get("type"),  # 优先使用template_id，兼容旧数据可以用type作为fallback
                    "is_active": True,  # 数据库状态字段
                    "template_code": os.path.join(folder, "strategy.py")  # 策略代码路径
                }
                
                if existing:
                    logger.info(f"更新策略模板: {template_data.get('name')} (版本 {template_data.get('version', '1.0.0')})")
                    await db.strategy_templates.update_one(
                        {"_id": existing["_id"]},
                        {"$set": template_dict}
                    )
                else:
                    logger.info(f"创建新策略模板: {template_data.get('name')} (版本 {template_data.get('version', '1.0.0')})")
                    await db.strategy_templates.insert_one(template_dict)
                
            except Exception as e:
                import traceback
                error_details = traceback.format_exc()
                logger.error(f"初始化策略文件夹 {folder} 失败: {str(e)}\n详细错误: {error_details}")
                continue
                
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"初始化策略模板数据失败: {str(e)}\n详细错误: {error_details}")
        raise 

async def init_data():
    """初始化所有数据"""
    # 加载策略模板数据
    await init_strategy_templates() 