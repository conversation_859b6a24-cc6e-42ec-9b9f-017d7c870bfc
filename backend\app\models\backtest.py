from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from bson import ObjectId

from ..core.data.db.base import MongoModel, db_manager
from .strategy import StrategyGroup

class BacktestConfig(BaseModel):
    """回测配置模型"""
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    initial_capital: float = Field(1000000.0, description="初始资金")
    transaction_fee: float = Field(0.0003, description="交易费率")
    slippage: float = Field(0.0001, description="滑点")
    position_size: float = Field(0.1, description="仓位大小")
    stop_loss: Optional[float] = Field(None, description="止损比例")
    take_profit: Optional[float] = Field(None, description="止盈比例")

class TradeRecord(BaseModel):
    """交易记录模型"""
    timestamp: datetime = Field(..., description="交易时间")
    symbol: str = Field(..., description="交易标的")
    direction: str = Field(..., description="交易方向")
    price: float = Field(..., description="交易价格")
    volume: float = Field(..., description="交易数量")
    commission: float = Field(..., description="手续费")
    slippage_cost: float = Field(..., description="滑点成本")

class BacktestResult(BaseModel):
    """回测结果模型"""
    total_returns: float = Field(..., description="总收益率")
    annual_returns: float = Field(..., description="年化收益率")
    max_drawdown: float = Field(..., description="最大回撤")
    sharpe_ratio: float = Field(..., description="夏普比率")
    volatility: float = Field(..., description="波动率")
    win_rate: float = Field(..., description="胜率")
    profit_factor: float = Field(..., description="盈亏比")
    total_trades: int = Field(..., description="总交易次数")
    daily_stats: Dict[str, Any] = Field(default_factory=dict, description="每日统计")

class BacktestRecord(MongoModel, BaseModel):
    """回测记录模型"""
    name: str = Field(..., description="回测名称")
    description: str = Field("", description="回测描述")
    user_id: str = Field(..., description="用户ID")
    strategy_group_id: str = Field(..., description="策略组ID")
    config: BacktestConfig = Field(..., description="回测配置")
    status: str = Field("pending", description="回测状态")
    progress: float = Field(0.0, description="回测进度")
    result: Optional[BacktestResult] = Field(None, description="回测结果")
    trades: List[TradeRecord] = Field(default_factory=list, description="交易记录")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            ObjectId: str,
            datetime: lambda v: v.isoformat()
        }
    
    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "backtest_records"
        
    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"
        
    @classmethod
    async def find_by_user(cls, user_id: str) -> List["BacktestRecord"]:
        """查找用户的回测记录"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find({"user_id": user_id})
        return [cls(**doc) async for doc in cursor]
        
    @classmethod
    async def find_by_id(cls, record_id: str) -> Optional["BacktestRecord"]:
        """根据ID查找回测记录"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        result = await db[cls.get_collection_name()].find_one({"_id": ObjectId(record_id)})
        if result:
            return cls(**result)
        return None 