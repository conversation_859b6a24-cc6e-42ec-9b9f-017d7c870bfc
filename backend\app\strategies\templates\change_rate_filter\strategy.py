"""
涨跌幅过滤策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from sqlalchemy import text

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class ChangeRateFilterStrategy(UnifiedStrategyCard):
    """涨跌幅过滤策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 检查缓存
            cache_key = f"{self._get_cache_key(context)}_{mode}"
            cached_data = self._data_cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    filtered_cache = cached_data[cached_data["代码"].isin(symbols)]
                    self._log(f"从缓存获取: {len(filtered_cache)}只股票")
                    return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 缓存数据
            self._data_cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["代码", "名称", "涨跌幅"])
            
            fields_str = ", ".join(fields)
            sql_query = f"SELECT {fields_str} FROM {table}"
            
            if symbols:
                placeholders = ", ".join([f"'{symbol}'" for symbol in symbols])
                sql_query += f" WHERE 代码 IN ({placeholders})"
            
            async with db_manager.get_session() as session:
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                return pd.DataFrame(rows, columns=fields)
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty or "涨跌幅" not in data.columns:
                return []
            
            # 获取参数
            operator = params.get("operator", template["parameters"]["operator"]["default"])
            value1 = params.get("change_rate1", template["parameters"]["change_rate1"]["default"])
            value2 = params.get("change_rate2", template["parameters"]["change_rate2"]["default"])
            
            self._log(f"涨跌幅筛选条件: {operator} {value1}%" + (f" - {value2}%" if operator == "区间" and value2 is not None else ""))
            
            # 根据条件筛选
            if operator == "大于":
                filtered_data = data[data["涨跌幅"] > value1]
            elif operator == "小于":
                filtered_data = data[data["涨跌幅"] < value1]
            else:  # 区间
                if value2 is None:
                    value2 = value1
                filtered_data = data[(data["涨跌幅"] >= value1) & (data["涨跌幅"] <= value2)]
            
            self._log(f"筛选出 {len(filtered_data)}只股票")
            
            # 使用统一的create_signal方法生成信号
            signals = [
                self.create_signal(
                    symbol=row["代码"],
                    name=row["名称"],
                    direction="BUY" if operator == "大于" else "SELL",
                    signal_type="technical",
                    confidence=0.8,
                    trigger_condition=f"涨跌幅{operator}_{value1}%" + (f"-{value2}%" if operator == "区间" else ""),
                    change_rate=float(row["涨跌幅"])
                )
                for _, row in filtered_data.iterrows()
            ]
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return [] 