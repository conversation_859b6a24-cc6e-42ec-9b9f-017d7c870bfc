"""
市场数据服务
"""
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd
import logging
import time
import asyncio
from sqlalchemy import text
from ..core.data.collectors.base import BaseDataCollector
from ..core.data.collectors.akshare import AKShareCollector
from ..core.data.processors.technical import TechnicalIndicator
from ..core.data.db.base import db_manager

logger = logging.getLogger(__name__)

class MarketDataService:
    """市场数据服务，负责数据聚合和处理"""
    
    def __init__(self):
        self._collector: BaseDataCollector = AKShareCollector()
        self._tech_indicator = TechnicalIndicator()
        # 服务层缓存
        self._cache = {}
        self._cache_ttl = {
            'market_status': 60,  # 市场状态缓存60秒
            'filtered_stocks': 300,  # 筛选结果缓存5分钟
            'board_data': 300,  # 板块数据缓存5分钟
            'realtime_quotes': 30  # 实时行情缓存30秒
        }
        
    async def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        try:
            return await self._collector.get_stock_list()
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
            
    async def get_realtime_quotes(
        self,
        stock_codes: List[str],
        indicators: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """获取实时行情数据
        
        Args:
            stock_codes: 股票代码列表
            indicators: 技术指标列表
            
        Returns:
            pd.DataFrame: 实时行情数据
        """
        try:
            # 生成缓存键
            cache_key = f"realtime_quotes_{','.join(sorted(stock_codes))}"
            if indicators:
                cache_key += f"_{','.join(sorted(indicators))}"
                
            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                logger.info(f"从缓存获取实时行情数据: {len(cached_data)} 条记录")
                return cached_data
                
            # 从数据库获取数据
            async with db_manager.get_session() as session:
                # 构建SQL查询，直接使用表中的实际列名
                placeholders = ", ".join([f"'{code}'" for code in stock_codes])
                sql_query = f"""
                SELECT * FROM stock_list_rt
                WHERE 代码 IN ({placeholders})
                """
                
                result = await session.execute(text(sql_query))
                rows = result.fetchall()
                
                # 转换为DataFrame
                if not rows:
                    logger.warning(f"未找到股票代码: {stock_codes}")
                    return pd.DataFrame()
                    
                # 获取列名
                columns = result.keys()
                data = pd.DataFrame(rows, columns=columns)
                
                # 根据实际表结构映射字段名
                column_mapping = {
                    '代码': 'symbol',
                    '名称': 'name',
                    '最新价': 'price',
                    '涨跌额': 'change',
                    '涨跌幅': 'changePercent',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '今开': 'open',
                    '最高': 'high',
                    '最低': 'low',
                    '换手率': 'turnoverRate',
                    '市盈率动态': 'pe',
                    '市净率': 'pb',
                    '总市值': 'marketCap',
                    '流通市值': 'floatMarketCap',
                    '六十日涨跌幅': 'day60_change',
                    'timestamp': 'updateTime'
                }
                
                # 只重命名存在的列
                rename_dict = {k: v for k, v in column_mapping.items() if k in data.columns}
                data = data.rename(columns=rename_dict)
                
                # 添加调试日志
                logger.info(f"数据库返回的列: {list(data.columns)}")
                if not data.empty:
                    logger.info(f"第一条记录: {data.iloc[0].to_dict()}")
                    # 检查总市值字段
                    if '总市值' in data.columns:
                        logger.info(f"总市值字段存在，值为: {data.iloc[0]['总市值']}")
                    else:
                        logger.info(f"总市值字段不存在，可用的字段有: {list(data.columns)}")
                
                # 计算技术指标
                if indicators and not data.empty:
                    data = await self._tech_indicator.calculate_multiple(data, indicators)
                
                # 更新缓存
                self._update_cache(cache_key, data, self._cache_ttl['realtime_quotes'])
                
                logger.info(f"从数据库获取实时行情数据: {len(data)} 条记录")
                return data
                
        except Exception as e:
            logger.error(f"获取实时行情失败: {str(e)}", exc_info=True)
            # 如果数据库查询失败，尝试使用API获取数据
            try:
                logger.info("尝试使用API获取实时行情数据")
                data = await self._collector.get_realtime_quotes(stock_codes)
                
                # 计算技术指标
                if indicators and not data.empty:
                    data = await self._tech_indicator.calculate_multiple(data, indicators)
                    
                return data
            except Exception as fallback_error:
                logger.error(f"API获取实时行情失败: {str(fallback_error)}", exc_info=True)
                return pd.DataFrame()
            
    async def get_historical_data(
        self,
        stock_codes: List[str],
        start_date: datetime,
        end_date: Optional[datetime] = None,
        frequency: str = "1d",
        indicators: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """获取历史数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率，如1d, 1w, 1m等
            indicators: 技术指标列表
            
        Returns:
            pd.DataFrame: 历史数据
        """
        try:
            # 获取原始数据
            data = await self._collector.get_historical_data(
                stock_codes,
                start_date,
                end_date,
                frequency
            )
            
            # 计算技术指标
            if indicators and not data.empty:
                # 使用并行计算多个指标
                data = await self._tech_indicator.calculate_multiple(data, indicators)
                    
            return data
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            raise
            
    async def filter_stocks(
        self,
        conditions: List[Dict[str, Any]],
        indicators: Optional[List[str]] = None
    ) -> List[str]:
        """根据条件筛选股票
        
        Args:
            conditions: 筛选条件列表
            indicators: 技术指标列表
            
        Returns:
            List[str]: 符合条件的股票代码列表
        """
        try:
            # 生成缓存键
            cache_key = f"filtered_stocks_{hash(str(conditions))}"
            if indicators:
                cache_key += f"_{hash(str(indicators))}"
                
            # 检查缓存
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 获取所有股票列表
            stock_list = await self.get_stock_list()
            
            # 获取实时数据
            data = await self.get_realtime_quotes(stock_list, indicators)
            
            if data.empty:
                return []
                
            # 应用筛选条件
            for condition in conditions:
                field = condition['field']
                operator = condition['operator']
                value = condition['value']
                
                if field not in data.columns:
                    logger.warning(f"字段 {field} 不存在，跳过此条件")
                    continue
                    
                try:
                    if operator == '大于':
                        data = data[data[field] > value]
                    elif operator == '小于':
                        data = data[data[field] < value]
                    elif operator == '等于':
                        data = data[data[field] == value]
                    elif operator == '介于':
                        if isinstance(value, (list, tuple)) and len(value) == 2:
                            data = data[(data[field] >= value[0]) & (data[field] <= value[1])]
                    elif operator == '不等于':
                        data = data[data[field] != value]
                except Exception as e:
                    logger.error(f"应用条件 {condition} 时出错: {e}")
                    continue
            
            # 获取结果
            result = data['symbol'].tolist() if not data.empty else []
            
            # 更新缓存
            self._update_cache(cache_key, result, self._cache_ttl['filtered_stocks'])
            
            return result
            
        except Exception as e:
            logger.error(f"筛选股票失败: {e}")
            return []
            
    async def get_board_data(self) -> Dict[str, Any]:
        """获取板块数据"""
        try:
            # 检查缓存
            cached_data = self._get_from_cache('board_data')
            if cached_data is not None:
                return cached_data
                
            # 获取板块数据
            data = await self._collector.get_board_data()
            
            # 更新缓存
            self._update_cache('board_data', data, self._cache_ttl['board_data'])
                
            return data
            
        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            return {}
            
    async def get_market_status(self) -> Dict[str, Any]:
        """获取市场状态"""
        try:
            # 检查缓存
            cached_data = self._get_from_cache('market_status')
            if cached_data is not None:
                return cached_data
                
            # 获取市场状态
            data = await self._collector.get_market_status()
            
            # 更新缓存
            self._update_cache('market_status', data, self._cache_ttl['market_status'])
            
            return data
            
        except Exception as e:
            logger.error(f"获取市场状态失败: {e}")
            return {}
            
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """从缓存获取数据
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存的数据，如果不存在或已过期则返回None
        """
        if key not in self._cache:
            return None
            
        cache_item = self._cache[key]
        if time.time() > cache_item['expire_time']:
            del self._cache[key]
            return None
            
        return cache_item['data']
        
    def _update_cache(self, key: str, data: Any, ttl: int) -> None:
        """更新缓存
        
        Args:
            key: 缓存键
            data: 要缓存的数据
            ttl: 缓存过期时间（秒）
        """
        self._cache[key] = {
            'data': data,
            'expire_time': time.time() + ttl
        }
        
    def _clean_expired_cache(self) -> None:
        """清理过期的缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, item in self._cache.items()
            if current_time > item['expire_time']
        ]
        for key in expired_keys:
            del self._cache[key]