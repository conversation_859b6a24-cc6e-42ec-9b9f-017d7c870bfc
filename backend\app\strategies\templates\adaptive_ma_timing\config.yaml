name: 自适应均线择时策略
description: 基于多重指数平滑均线的高级择时策略，具有自适应周期调整和趋势强度确认功能
version: 1.0.0
author: QuantCard团队
tags:
  - 技术分析
  - 均线系统
  - 自适应算法
  - 择时策略
  - 趋势跟踪

strategy_class_name: AdaptiveMATimingStrategy

parameters:
  ma_type:
    type: string
    default: "EMA"
    options: ["SMA", "EMA", "DEMA", "TEMA"]
    description: 移动平均线类型
  base_period:
    type: int
    default: 20
    min: 5
    max: 100
    description: 基础均线周期
  adaptive_enabled:
    type: boolean
    default: true
    description: 是否启用自适应周期调整
  volatility_lookback:
    type: int
    default: 20
    min: 10
    max: 50
    description: 波动率计算回溯期
  min_adx:
    type: float
    default: 20.0
    min: 10.0
    max: 50.0
    description: 最小ADX趋势强度要求
  min_volume_ratio:
    type: float
    default: 1.2
    min: 1.0
    max: 3.0
    description: 最小成交量比率要求
  confirmation_periods:
    type: int
    default: 2
    min: 1
    max: 5
    description: 信号确认周期数

data_sources:
  timing:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 200  # 需要足够多的数据计算指标
    
  backtest:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 500  # 回测需要更多历史数据
    
  monitor:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 200

outputs:
  timing:
    type: signal
    fields:
      - symbol
      - name
      - direction
      - confidence
      - trigger_condition
      - current_price
      - ma_fast
      - ma_slow
      - adx
      - trend_strength
      - stop_loss
      - atr
      - volume_ratio
      
  backtest:
    type: backtest_result
    fields:
      - symbol
      - name
      - entry_time
      - entry_price
      - exit_time
      - exit_price
      - return_pct
      - signal_type
      
  monitor:
    type: alert
    fields:
      - symbol
      - name
      - alert_type
      - trigger_condition
      - current_price
      - trend_strength
      - alert_time