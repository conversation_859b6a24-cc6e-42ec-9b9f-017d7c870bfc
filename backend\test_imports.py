#!/usr/bin/env python3
"""
测试导入修复是否成功
"""

def test_strategy_imports():
    """测试策略相关的导入"""
    try:
        print("🔍 测试策略相关导入...")
        
        # 测试策略组导入
        print("  - 测试 StrategyGroup 导入...")
        from app.models.strategy_group import StrategyGroup
        print("    ✅ StrategyGroup 导入成功")
        
        # 测试策略卡导入
        print("  - 测试 StrategyCard 导入...")
        from app.models.strategy import StrategyCard
        print("    ✅ StrategyCard 导入成功")
        
        # 测试API端点导入
        print("  - 测试策略API端点导入...")
        from app.api.v1.endpoints.strategies import router
        print("    ✅ 策略API端点导入成功")
        
        # 测试回测服务导入
        print("  - 测试回测服务导入...")
        from app.services.backtest import BacktestService
        print("    ✅ 回测服务导入成功")
        
        # 测试主应用导入
        print("  - 测试主应用导入...")
        from app.main import app
        print("    ✅ 主应用导入成功")
        
        print("🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_strategy_imports()
    exit(0 if success else 1)
