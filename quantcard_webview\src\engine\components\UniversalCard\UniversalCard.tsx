/**
 * 🃏 UniversalCard - 统一卡牌组件规范
 * 适配所有场景的卡牌系统，支持多种显示模式和交互方式
 */

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import styled, { css } from 'styled-components'

import type { StrategyTemplate, StrategyCardInstance, StrategyGroup } from '../../../types/game'

// 🎯 策略模板数据接口 (基础模板)
export interface StrategyTemplateData extends StrategyTemplate {
  // 游戏属性
  level?: number
  experience?: number
  power: number       // 攻击力（预期收益）
  defense: number     // 防御力（风险控制）
  speed?: number      // 执行速度
  cost?: number       // 能量消耗
}

// 🎯 配置策略卡数据接?(扩展基础类型)
export interface ConfiguredCardData extends StrategyCardInstance {
  level: number
  experience: number
  configuredAt: string
  lastUsed?: string
  
  // 游戏属性（根据配置计算）
  power: number       // 攻击力（预期收益）
  defense: number     // 防御力（风险控制）
  speed: number       // 执行速度
  cost: number        // 能量消耗
  execution_mode: 'sequential' | 'parallel'
  creation_mode: 'simple' | 'canvas'
  status: 'active' | 'inactive' | 'error'
  performance_metrics: {
    total_return: number
    sharpe_ratio: number
    max_drawdown: number
    win_rate: number
  }
  canvas_config?: any
  execution_flow?: any[]
}

// 🎯 策略组数据接口
export interface StrategyGroupData extends StrategyGroup {
  // 游戏属性
  level?: number
  power: number
  defense: number
  speed?: number
  cost?: number
}

// 🎯 统一卡牌数据联合类型
export type UnifiedCardData = StrategyTemplateData | ConfiguredCardData | StrategyGroupData

// 🎨 卡牌组件属性接口
export interface UniversalCardProps {
  // 核心数据
  card: UnifiedCardData
  
  // 显示变体
  variant: 'collection' | 'battle' | 'shop' | '3d' | 'mini' | 'inventory'
  
  // 尺寸规格
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  
  // 交互状态
  interactive?: boolean
  draggable?: boolean
  selectable?: boolean
  selected?: boolean
  disabled?: boolean
  
  // 视觉效果
  glowEffect?: boolean
  particleEffect?: boolean
  showRarityBorder?: boolean
  showStats?: boolean
  showLevel?: boolean
  
  // 事件处理
  onCardClick?: (card: UnifiedCardData) => void
  onCardHover?: (card: UnifiedCardData | null) => void
  onCardDragStart?: (card: UnifiedCardData) => void
  onCardDragEnd?: (card: UnifiedCardData, position: {x: number, y: number}) => void
  
  // 样式定制
  className?: string
  style?: React.CSSProperties
}

// 🌈 稀有度颜色映射
export const RarityColors = {
  common: '#6B7280',
  rare: '#3B82F6',
  epic: '#A855F7',
  legendary: '#F59E0B',
  mythic: '#EF4444',
} as const

// 📏 卡牌尺寸映射
export const CardSizes = {
  xs: { width: 60, height: 84, fontSize: 8 },
  sm: { width: 120, height: 168, fontSize: 10 },
  md: { width: 180, height: 252, fontSize: 12 },
  lg: { width: 240, height: 336, fontSize: 14 },
  xl: { width: 300, height: 420, fontSize: 16 },
} as const

// 🎪 卡牌容器样式
const CardContainer = styled(motion.div)<{
  $rarity: keyof typeof RarityColors
  $size: keyof typeof CardSizes
  $interactive: boolean
  $selected: boolean
  $disabled: boolean
}>`
  position: relative;
  width: ${props => CardSizes[props.$size].width}px;
  height: ${props => CardSizes[props.$size].height}px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 2px solid ${props => RarityColors[props.$rarity]};
  cursor: ${props => props.$interactive ? 'pointer' : 'default'};
  user-select: none;
  overflow: hidden;
  
  // 🌟 浅色优雅阴影效果
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  
  // 🎯 选中状?  ${props => props.$selected && css`
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 0 0 3px ${RarityColors[props.$rarity]}40,
      inset 0 1px 0 rgba(255, 255, 255, 1);
    transform: translateY(-4px);
  `}
  
  // 🚫 禁用状�?  ${props => props.$disabled && css`
    opacity: 0.4;
    cursor: not-allowed;
    filter: grayscale(0.8);
    background: rgba(255, 255, 255, 0.5);
  `}
  
  // 🎯 交互效果
  ${props => props.$interactive && css`
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    &:hover {
      transform: translateY(-3px) scale(1.02);
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 0 0 2px ${RarityColors[props.$rarity]}30,
        inset 0 1px 0 rgba(255, 255, 255, 1);
    }
    
    &:active {
      transform: translateY(-1px) scale(0.98);
      transition-duration: 0.1s;
    }
  `}
`

// 🖼�?卡牌图片区域
const CardArtwork = styled.div<{ $size: keyof typeof CardSizes }>`
  position: relative;
  width: 100%;
  height: 60%;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1), 
    rgba(139, 92, 246, 0.1),
    rgba(6, 182, 212, 0.08)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => CardSizes[props.$size].fontSize * 2}px;
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
`

// 📝 卡牌信息区域
const CardInfo = styled.div<{ $size: keyof typeof CardSizes; $variant?: UniversalCardProps['variant'] }>`
  padding: ${props => props.$variant === 'inventory' ? '6px 8px' : '10px'};
  height: 40%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: ${props => CardSizes[props.$size].fontSize}px;
  background: rgba(255, 255, 255, 0.3);
`

// 🏷�?卡牌标题
const CardTitle = styled.div<{ $variant?: UniversalCardProps['variant'] }>`
  color: #1e293b;
  font-weight: 600;
  text-align: center;
  margin-bottom: ${props => props.$variant === 'inventory' ? '2px' : '4px'};
  line-height: ${props => props.$variant === 'inventory' ? 1.15 : 1.25};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
`

// 🎆 稀有度显示
const RarityStars = styled.div<{ $rarity: keyof typeof RarityColors; $variant?: UniversalCardProps['variant'] }>`
  text-align: center;
  color: ${props => RarityColors[props.$rarity]};
  margin-bottom: ${props => props.$variant === 'inventory' ? '2px' : '4px'};
`

// 📊 属性数值区域
const StatsRow = styled.div<{ $variant?: UniversalCardProps['variant'] }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${props => props.$variant === 'inventory' ? '1px' : '2px'};
`

const StatValue = styled.span<{ $color: string }>`
  color: ${props => props.$color};
  font-size: 0.8em;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
`

// 🎆 粒子特效容器
const ParticleContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
`

// 🎬 动画配置
export const CardAnimationVariants = {
  // 默认状态
  initial: { 
    scale: 1, 
    rotateY: 0, 
    opacity: 1,
    y: 0 
  },
  
  // 悬停状态
  hover: { 
    scale: 1.05, 
    rotateY: 5,
    y: -4,
    transition: { duration: 0.2 }
  },
  
  // 点击状态
  tap: { 
    scale: 0.95, 
    transition: { duration: 0.1 }
  },
  
  // 拖拽状态
  drag: { 
    scale: 1.1, 
    rotate: [0, 2, -2, 0],
    zIndex: 1000,
    boxShadow: '0 20px 60px rgba(0,255,255,0.4)'
  },
  
  // 退场动画
  exit: {
    scale: 0.8,
    opacity: 0,
    y: -20,
    transition: { duration: 0.2 }
  }
}

// 🃏 统一卡牌组件实现
export const UniversalCard: React.FC<UniversalCardProps> = ({
  card,
  variant = 'collection',
  size = 'md',
  interactive = true,
  draggable = false,
  selectable = false,
  selected = false,
  disabled = false,
  glowEffect = true,
  particleEffect = false,
  showRarityBorder = true,
  showStats = true,
  showLevel = false,
  onCardClick,
  onCardHover,
  onCardDragStart,
  onCardDragEnd,
  className = '',
  style = {}
}) => {
  // 🎯 处理点击事件
  const handleClick = () => {
    if (!disabled && interactive && onCardClick) {
      onCardClick(card)
    }
  }

  // 🎪 处理悬停事件
  const handleMouseEnter = () => {
    if (!disabled && onCardHover) {
      onCardHover(card)
    }
  }

  const handleMouseLeave = () => {
    if (onCardHover) {
      onCardHover(null)
    }
  }

  // 🎆 生成稀有度星星
  const getRarityStars = (rarity: string) => {
    const starCount = {
      common: 1,
      rare: 2,
      epic: 3,
      legendary: 4,
      mythic: 5
    }[rarity] || 1
    
    return '★'.repeat(starCount)
  }

  // 🎨 获取卡牌图标
  const getCardIcon = (type: string) => {
    const icons = {
      filter: '📈',
      timing: '⏱️',
      risk_management: '🛡️'
    }
    return icons[type as keyof typeof icons] || '📊'
  }

  return (
    <CardContainer
      $rarity={card.rarity}
      $size={size}
      $interactive={interactive && !disabled}
      $selected={selected}
      $disabled={disabled}
      className={className}
      style={style}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      variants={CardAnimationVariants}
      whileHover={interactive && !disabled ? 'hover' : undefined}
      whileTap={interactive && !disabled ? 'tap' : undefined}
      initial="initial"
      animate="initial"
      exit="exit"
    >
      {/* 🖼️ 卡牌艺术作品区域 */}
      <CardArtwork $size={size}>
        {card.artwork ? (
          <img 
            src={card.artwork} 
            alt={card.name}
            style={{ 
              width: '100%', 
              height: '100%', 
              objectFit: 'cover',
              borderRadius: '8px 8px 0 0'
            }}
          />
        ) : (
          <span>{card.iconUrl ? '🖼️' : getCardIcon(card.type)}</span>
        )}
      </CardArtwork>

      {/* 📝 卡牌信息区域 */}
      <CardInfo $size={size} $variant={variant}>
        {/* 🏷️ 卡牌标题 */}
        <CardTitle $variant={variant}>{card.name}</CardTitle>

        {/* 📊 属性数值 */}
        {showStats && (
          <StatsRow $variant={variant}>
            <StatValue $color="#0891b2">
              🗡️{card.power}
            </StatValue>
            <StatValue $color="#059669">
              🛡️{card.defense}
            </StatValue>
          </StatsRow>
        )}

        {/* 🔢 等级显示 */}
        {showLevel && (
          <div style={{ 
            textAlign: 'center', 
            color: '#d97706', 
            fontSize: '0.8em',
            fontWeight: '600',
            textShadow: '0 1px 2px rgba(255, 255, 255, 0.8)'
          }}>
            Lv.{card.level}
          </div>
        )}
      </CardInfo>

      {/* 🎆 粒子特效 */}
      {particleEffect && (
        <ParticleContainer>
          {/* 粒子特效组件将在这里渲染 */}
        </ParticleContainer>
      )}
    </CardContainer>
  )
}

type SceneConfigProps = Pick<UniversalCardProps,
  'size' | 'interactive' | 'draggable' | 'selectable' |
  'glowEffect' | 'particleEffect' | 'showRarityBorder' | 'showStats' | 'showLevel'
>

export const createCardForScene = (
  card: UnifiedCardData, 
  scene: UniversalCardProps['variant']
): SceneConfigProps => {
  const sceneConfigs = {
    collection: {
      size: 'md' as const,
      interactive: true,
      showStats: true,
      showLevel: true,
      glowEffect: true
    },
    battle: {
      size: 'sm' as const,
      interactive: true,
      draggable: true,
      showStats: true,
      particleEffect: true
    },
    shop: {
      size: 'lg' as const,
      interactive: true,
      showStats: true,
      glowEffect: true
    },
    '3d': {
      size: 'xl' as const,
      interactive: true,
      showStats: true,
      showLevel: true,
      glowEffect: true,
      particleEffect: true
    },
    mini: {
      size: 'xs' as const,
      interactive: false,
      showStats: false,
      showLevel: false
    },
    inventory: {
      size: 'sm' as const,
      interactive: true,
      selectable: true,
      showStats: true,
      showLevel: true
    }
  }
  
  return sceneConfigs[scene] || sceneConfigs.collection
}

export default UniversalCard
