"""
ATR动态止损策略
基于平均真实范围(ATR)的智能风险管理策略，提供动态止损、止盈和仓位管理功能
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path
import talib as ta

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

class ATRDynamicStopStrategy(UnifiedStrategyCard):
    """ATR动态止损策略
    
    该策略功能：
    1. ATR动态止损：根据市场波动率调整止损位
    2. 追踪止损：价格有利时动态调整止损位
    3. ATR止盈：基于ATR的动态止盈目标
    4. 仓位管理：根据ATR波动率调整仓位大小
    5. 风险预警：ATR异常变化的风险提示
    6. 多时间框架：支持不同周期的ATR分析
    """
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                try:
                    processed_df = await self._process_kline_data(df, context.parameters)
                    if not processed_df.empty:
                        processed_data[symbol] = processed_df
                        self._log(f"成功处理股票 {symbol} 的ATR数据，共{len(processed_df)}条记录")
                    else:
                        self._log(f"股票 {symbol} 数据处理后为空", "warning")
                except Exception as e:
                    self._log(f"处理股票 {symbol} 数据失败: {str(e)}", "error")
            
            return processed_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _process_kline_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """处理K线数据，计算ATR相关指标"""
        try:
            if df.empty:
                return pd.DataFrame()
            
            # 确保数据按时间排序
            df = df.sort_values('time').copy()
            
            # 获取参数
            atr_period = int(params.get("atr_period", 14))
            stop_multiplier = float(params.get("stop_multiplier", 2.0))
            profit_multiplier = float(params.get("profit_multiplier", 3.0))
            
            # 计算ATR相关指标
            df = self._calculate_atr_indicators(df, atr_period)
            
            # 计算动态止损止盈位
            df = self._calculate_dynamic_stops(df, stop_multiplier, profit_multiplier)
            
            # 计算仓位管理指标
            df = self._calculate_position_sizing(df, params)
            
            # 生成风险管理信号
            df = self._generate_risk_signals(df, params)
            
            return df
            
        except Exception as e:
            self._log(f"处理K线数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _calculate_atr_indicators(self, df: pd.DataFrame, period: int) -> pd.DataFrame:
        """计算ATR相关指标"""
        try:
            high_values = df['high'].values
            low_values = df['low'].values
            close_values = df['close'].values
            
            # 计算基础ATR
            df['atr'] = ta.ATR(high_values, low_values, close_values, timeperiod=period)
            
            # ATR百分比（相对于价格的ATR）
            df['atr_pct'] = (df['atr'] / df['close']) * 100
            
            # ATR趋势（ATR的移动平均）
            df['atr_ma'] = df['atr'].rolling(window=period).mean()
            df['atr_trend'] = df['atr'] / df['atr_ma'] - 1
            
            # ATR波动率状态
            atr_std = df['atr'].rolling(window=period * 2).std()
            atr_mean = df['atr'].rolling(window=period * 2).mean()
            df['atr_zscore'] = (df['atr'] - atr_mean) / atr_std
            
            # ATR状态分类
            conditions = [
                df['atr_zscore'] > 1,
                df['atr_zscore'] < -1
            ]
            choices = ['high_volatility', 'low_volatility']
            df['volatility_state'] = np.select(conditions, choices, default='normal')
            
            # 真实范围组件分析
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = (df['high'] - df['close'].shift(1)).abs()
            df['tr3'] = (df['low'] - df['close'].shift(1)).abs()
            
            return df
            
        except Exception as e:
            self._log(f"计算ATR指标失败: {str(e)}", "error")
            return df
    
    def _calculate_dynamic_stops(self, df: pd.DataFrame, stop_multiplier: float, 
                                profit_multiplier: float) -> pd.DataFrame:
        """计算动态止损止盈位"""
        try:
            # 动态止损位（多头）
            df['stop_long'] = df['close'] - df['atr'] * stop_multiplier
            
            # 动态止损位（空头）
            df['stop_short'] = df['close'] + df['atr'] * stop_multiplier
            
            # 动态止盈位（多头）
            df['profit_long'] = df['close'] + df['atr'] * profit_multiplier
            
            # 动态止盈位（空头）
            df['profit_short'] = df['close'] - df['atr'] * profit_multiplier
            
            # 追踪止损（多头）- 只能向上调整
            df['trailing_stop_long'] = df['stop_long'].copy()
            for i in range(1, len(df)):
                if pd.notna(df.iloc[i-1]['trailing_stop_long']):
                    df.iloc[i, df.columns.get_loc('trailing_stop_long')] = max(
                        df.iloc[i]['stop_long'],
                        df.iloc[i-1]['trailing_stop_long']
                    )
            
            # 追踪止损（空头）- 只能向下调整
            df['trailing_stop_short'] = df['stop_short'].copy()
            for i in range(1, len(df)):
                if pd.notna(df.iloc[i-1]['trailing_stop_short']):
                    df.iloc[i, df.columns.get_loc('trailing_stop_short')] = min(
                        df.iloc[i]['stop_short'],
                        df.iloc[i-1]['trailing_stop_short']
                    )
            
            # 止损距离（百分比）
            df['stop_distance_pct'] = (df['atr'] * stop_multiplier / df['close']) * 100
            
            return df
            
        except Exception as e:
            self._log(f"计算动态止损失败: {str(e)}", "error")
            return df
    
    def _calculate_position_sizing(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算仓位管理指标"""
        try:
            # 获取参数
            risk_per_trade = float(params.get("risk_per_trade", 0.02))  # 单笔风险2%
            max_position_size = float(params.get("max_position_size", 0.2))  # 最大仓位20%
            
            # 基于ATR的仓位大小计算
            # 仓位 = 风险金额 / 止损距离
            df['position_size'] = risk_per_trade / (df['stop_distance_pct'] / 100)
            
            # 限制最大仓位
            df['position_size'] = np.minimum(df['position_size'], max_position_size)
            
            # 波动率调整
            # 高波动时减少仓位，低波动时增加仓位
            volatility_adjustment = 1 / (1 + df['atr_pct'] / 5)  # ATR百分比越高，调整越大
            df['adjusted_position_size'] = df['position_size'] * volatility_adjustment
            
            # 仓位风险评级
            conditions = [
                df['atr_pct'] > 5,  # 高风险
                df['atr_pct'] > 3,  # 中风险
                df['atr_pct'] > 1   # 低风险
            ]
            choices = ['high_risk', 'medium_risk', 'low_risk']
            df['risk_level'] = np.select(conditions, choices, default='very_low_risk')
            
            return df
            
        except Exception as e:
            self._log(f"计算仓位管理失败: {str(e)}", "error")
            return df
    
    def _generate_risk_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成风险管理信号"""
        try:
            # 获取参数
            max_atr_change = float(params.get("max_atr_change", 0.5))  # 最大ATR变化50%
            
            # 初始化信号列
            df['risk_signal'] = 'NORMAL'
            df['risk_level_score'] = 0.5
            df['action_required'] = False
            
            for i in range(1, len(df)):
                current = df.iloc[i]
                previous = df.iloc[i-1]
                
                # ATR急剧变化警告
                if pd.notna(previous['atr']) and previous['atr'] > 0:
                    atr_change = (current['atr'] - previous['atr']) / previous['atr']
                    
                    if abs(atr_change) > max_atr_change:
                        df.iloc[i, df.columns.get_loc('risk_signal')] = 'HIGH_VOLATILITY_ALERT'
                        df.iloc[i, df.columns.get_loc('action_required')] = True
                        df.iloc[i, df.columns.get_loc('risk_level_score')] = 0.9
                
                # 止损位触发警告
                if current['close'] <= current['trailing_stop_long']:
                    df.iloc[i, df.columns.get_loc('risk_signal')] = 'STOP_LOSS_TRIGGERED'
                    df.iloc[i, df.columns.get_loc('action_required')] = True
                    df.iloc[i, df.columns.get_loc('risk_level_score')] = 0.95
                
                # 止盈位触发提示
                if current['close'] >= current['profit_long']:
                    df.iloc[i, df.columns.get_loc('risk_signal')] = 'TAKE_PROFIT_AVAILABLE'
                    df.iloc[i, df.columns.get_loc('action_required')] = False
                    df.iloc[i, df.columns.get_loc('risk_level_score')] = 0.3
                
                # 高风险仓位警告
                if current['risk_level'] == 'high_risk':
                    df.iloc[i, df.columns.get_loc('risk_signal')] = 'REDUCE_POSITION_SIZE'
                    df.iloc[i, df.columns.get_loc('action_required')] = True
                    df.iloc[i, df.columns.get_loc('risk_level_score')] = 0.8
            
            return df
            
        except Exception as e:
            self._log(f"生成风险信号失败: {str(e)}", "error")
            return df

    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取最新的风险信号
                latest = df.iloc[-1]
                risk_signals = df[df['action_required'] == True].tail(3)
                
                # 生成当前风险状态信号
                current_risk_signal = self.create_signal(
                    symbol=symbol,
                    name=symbol,
                    direction="HOLD",  # 风险管理策略主要是HOLD和管理信号
                    signal_type="risk_management",
                    confidence=latest['risk_level_score'],
                    trigger_condition=f"风险状态:{latest['risk_signal']}",
                    current_price=float(latest['close']),
                    atr=float(latest['atr']) if pd.notna(latest['atr']) else 0,
                    atr_pct=float(latest['atr_pct']) if pd.notna(latest['atr_pct']) else 0,
                    stop_long=float(latest['stop_long']) if pd.notna(latest['stop_long']) else 0,
                    stop_short=float(latest['stop_short']) if pd.notna(latest['stop_short']) else 0,
                    profit_long=float(latest['profit_long']) if pd.notna(latest['profit_long']) else 0,
                    profit_short=float(latest['profit_short']) if pd.notna(latest['profit_short']) else 0,
                    trailing_stop_long=float(latest['trailing_stop_long']) if pd.notna(latest['trailing_stop_long']) else 0,
                    trailing_stop_short=float(latest['trailing_stop_short']) if pd.notna(latest['trailing_stop_short']) else 0,
                    position_size=float(latest['adjusted_position_size']) if pd.notna(latest['adjusted_position_size']) else 0,
                    risk_level=str(latest['risk_level']),
                    volatility_state=str(latest['volatility_state']),
                    stop_distance_pct=float(latest['stop_distance_pct']) if pd.notna(latest['stop_distance_pct']) else 0
                )
                signals.append(current_risk_signal)
                
                # 处理需要行动的风险信号
                for _, row in risk_signals.iterrows():
                    risk_type = row['risk_signal']
                    
                    if risk_type == 'STOP_LOSS_TRIGGERED':
                        direction = 'SELL'
                        confidence = 0.95
                        condition = 'ATR止损位触发'
                    elif risk_type == 'HIGH_VOLATILITY_ALERT':
                        direction = 'HOLD'
                        confidence = 0.8
                        condition = 'ATR波动率异常增加'
                    elif risk_type == 'REDUCE_POSITION_SIZE':
                        direction = 'HOLD'
                        confidence = 0.7
                        condition = '高风险-建议减仓'
                    elif risk_type == 'TAKE_PROFIT_AVAILABLE':
                        direction = 'SELL'
                        confidence = 0.6
                        condition = 'ATR止盈位可考虑'
                    else:
                        continue
                    
                    risk_action_signal = self.create_signal(
                        symbol=symbol,
                        name=symbol,
                        direction=direction,
                        signal_type="risk_management",
                        confidence=confidence,
                        trigger_condition=condition,
                        current_price=float(row['close']),
                        atr=float(row['atr']) if pd.notna(row['atr']) else 0,
                        stop_distance_pct=float(row['stop_distance_pct']) if pd.notna(row['stop_distance_pct']) else 0,
                        risk_signal_type=risk_type,
                        recommended_position=float(row['adjusted_position_size']) if pd.notna(row['adjusted_position_size']) else 0
                    )
                    signals.append(risk_action_signal)
            
            self._log(f"ATR动态止损策略生成{len(signals)}个风险管理信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []

    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            self._log("开始执行ATR动态止损策略")
            
            # 获取参数信息
            atr_period = context.parameters.get("atr_period", 14)
            stop_multiplier = context.parameters.get("stop_multiplier", 2.0)
            profit_multiplier = context.parameters.get("profit_multiplier", 3.0)
            risk_per_trade = context.parameters.get("risk_per_trade", 0.02)
            
            self._log(f"风险管理参数: ATR周期={atr_period}, 止损倍数={stop_multiplier}, "
                     f"止盈倍数={profit_multiplier}, 单笔风险={risk_per_trade*100}%")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            if not data:
                self._log("未获取到有效数据", "warning")
                return []
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 统计信号类型
            status_signals = len([s for s in signals if s.direction == "HOLD"])
            stop_loss_signals = len([s for s in signals if "止损" in s.trigger_condition])
            take_profit_signals = len([s for s in signals if "止盈" in s.trigger_condition])
            risk_alert_signals = len([s for s in signals if "风险" in s.trigger_condition or "减仓" in s.trigger_condition])
            
            self._log(f"风险管理执行完成: 状态信号{status_signals}个, 止损信号{stop_loss_signals}个, "
                     f"止盈信号{take_profit_signals}个, 风险警告{risk_alert_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []