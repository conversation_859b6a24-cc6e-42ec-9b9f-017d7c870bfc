/**
 * 🎯 QuantCard 策略组视觉组�? * 实现策略组的特殊视觉设计和交互效�? */

import React, { useState } from 'react'
import styled, { css, keyframes } from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'

// 🌊 策略组动画效�?const groupActiveGlow = keyframes`
  0% { 
    box-shadow: 0 0 25px rgba(16,185,129,0.4), 0 8px 32px rgba(0,0,0,0.3);
  }
  50% { 
    box-shadow: 0 0 35px rgba(16,185,129,0.6), 0 12px 48px rgba(0,0,0,0.4);
  }
  100% { 
    box-shadow: 0 0 25px rgba(16,185,129,0.4), 0 8px 32px rgba(0,0,0,0.3);
  }
`

const groupErrorPulse = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
`

const flowPulse = keyframes`
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
`

const sequentialFlow = keyframes`
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
`

// 🎯 策略组状态类�?export type StrategyGroupStatus = 'active' | 'inactive' | 'error' | 'paused'
export type ExecutionMode = 'sequential' | 'parallel'

// 🎨 策略组样式配�?const groupStatusStyles = {
  active: css`
    border-color: #10B981;
    animation: ${groupActiveGlow} 3s ease-in-out infinite;
    
    .group-status {
      background: rgba(16,185,129,0.2);
      color: #10B981;
    }
  `,
  
  inactive: css`
    border-color: #6B7280;
    box-shadow: 0 0 20px rgba(107,114,128,0.3), 0 8px 32px rgba(0,0,0,0.3);
    
    .group-status {
      background: rgba(107,114,128,0.2);
      color: #6B7280;
    }
  `,
  
  error: css`
    border-color: #EF4444;
    box-shadow: 0 0 25px rgba(239,68,68,0.4), 0 8px 32px rgba(0,0,0,0.3);
    animation: ${groupErrorPulse} 1s ease-in-out infinite;
    
    .group-status {
      background: rgba(239,68,68,0.2);
      color: #EF4444;
    }
  `,
  
  paused: css`
    border-color: #F59E0B;
    box-shadow: 0 0 20px rgba(245,158,11,0.3), 0 8px 32px rgba(0,0,0,0.3);
    
    .group-status {
      background: rgba(245,158,11,0.2);
      color: #F59E0B;
    }
  `
}

import type { StrategyGroup } from '../../../types/game'

// 🎯 策略组卡片扩展数据接�?export interface StrategyGroupCardData extends StrategyGroup {
  cards: Array<{
    id: string
    name: string
    icon: string
    level: 1 | 2 | 3 | 4 | 5
    power: number
    defense: number
  }>
  last_execution?: string
}

interface StrategyGroupCardProps {
  group: StrategyGroupCardData
  size?: 'sm' | 'md' | 'lg'
  onEdit?: (group: StrategyGroupCardData) => void
  onExecute?: (group: StrategyGroupCardData) => void
  onStop?: (group: StrategyGroupCardData) => void
  onDelete?: (group: StrategyGroupCardData) => void
  className?: string
}

// 🎨 样式化组�?const GroupContainer = styled(motion.div)<{ 
  status: StrategyGroupStatus
  size: string 
}>`
  background: linear-gradient(135deg, #1A1A2E 0%, #2D3748 100%);
  border: 3px solid #00FFFF;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  
  ${props => groupStatusStyles[props.status]}
  
  width: ${props => {
    switch(props.size) {
      case 'sm': return '300px'
      case 'lg': return '600px'
      default: return '480px'
    }
  }};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 
      0 0 30px rgba(0,255,255,0.5),
      0 12px 48px rgba(0,0,0,0.4);
  }
`

const GroupHeader = styled.div`
  padding: 16px 20px;
  background: linear-gradient(90deg, 
    rgba(0,255,255,0.1) 0%, 
    rgba(255,0,255,0.1) 100%);
  border-bottom: 1px solid rgba(0,255,255,0.2);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`

const GroupTitleSection = styled.div`
  flex: 1;
`

const GroupTitle = styled.h3`
  font-size: 18px;
  color: #FFFFFF;
  font-weight: 700;
  margin: 0 0 4px 0;
  line-height: 1.2;
`

const GroupSubtitle = styled.div`
  font-size: 12px;
  color: #CCCCCC;
  display: flex;
  align-items: center;
  gap: 8px;
`

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
`

const ExecutionModeIndicator = styled.div<{ mode: ExecutionMode }>`
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: #00FFFF;
  
  &::before {
    content: '${props => (props.mode === "sequential" ? "⇢" : "⇉")}';
    font-size: 12px;
  }
`

// 🔄 执行流程可视化
const FlowContainer = styled.div<{ mode: ExecutionMode }>`
  padding: 20px;
  min-height: 120px;
  display: ${props => props.mode === 'sequential' ? 'flex' : 'grid'};
  ${props => props.mode === 'sequential' ? css`
    align-items: center;
    gap: 12px;
  ` : css`
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 12px;
    place-items: center;
  `}
`

const FlowCard = styled.div`
  width: 80px;
  height: 60px;
  background: rgba(0,255,255,0.1);
  border: 1px solid #00FFFF;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #FFFFFF;
  position: relative;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(0,255,255,0.2);
    transform: scale(1.05);
  }
`

const FlowArrow = styled.div`
  color: #00FFFF;
  font-size: 16px;
  animation: ${flowPulse} 2s ease-in-out infinite;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00FFFF, transparent);
    animation: ${sequentialFlow} 3s ease-in-out infinite;
  }
`

const ParallelHub = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #00FFFF;
  border-radius: 50%;
  box-shadow: 0 0 12px rgba(0,255,255,0.6);
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    border: 1px dashed rgba(0,255,255,0.3);
    border-radius: 50%;
    animation: ${flowPulse} 2s ease-in-out infinite;
  }
`

// 📊 性能指标显示
const MetricsContainer = styled.div`
  padding: 16px 20px;
  background: rgba(0,0,0,0.2);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
`

const Metric = styled.div<{ positive?: boolean }>`
  text-align: center;
  
  .metric-value {
    font-size: 16px;
    font-weight: 700;
    color: ${props => 
      props.positive === true ? '#10B981' : 
      props.positive === false ? '#EF4444' : '#00FFFF'};
    display: block;
  }
  
  .metric-label {
    font-size: 10px;
    color: #CCCCCC;
    margin-top: 2px;
    display: block;
  }
`

// 🎮 操作按钮区域
const ActionsContainer = styled.div`
  padding: 16px 20px;
  border-top: 1px solid rgba(0,255,255,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
`

const ActionButton = styled.button<{ primary?: boolean, danger?: boolean }>`
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  
  ${props => props.primary ? css`
    background: #00FFFF;
    color: #000000;
    
    &:hover {
      background: #33FFFF;
      transform: scale(1.1);
    }
  ` : props.danger ? css`
    background: rgba(239,68,68,0.2);
    color: #EF4444;
    
    &:hover {
      background: rgba(239,68,68,0.3);
      transform: scale(1.1);
    }
  ` : css`
    background: rgba(0,255,255,0.1);
    color: #00FFFF;
    
    &:hover {
      background: rgba(0,255,255,0.2);
      transform: scale(1.1);
    }
  `}
`

// 🎯 主要的策略组卡片组件
export const StrategyGroupCard: React.FC<StrategyGroupCardProps> = ({
  group,
  size = 'md',
  onEdit,
  onExecute,
  onStop,
  onDelete,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const getStatusIcon = (status: StrategyGroupStatus): string => {
    const icons = {
      active: '🟢',
      inactive: '�?,
      error: '🔴',
      paused: '🟡'
    }
    return icons[status]
  }

  const getStatusText = (status: StrategyGroupStatus): string => {
    const texts = {
      active: '运行�?,
      inactive: '未启�?,
      error: '错误',
      paused: '已暂�?
    }
    return texts[status]
  }

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`
  }

  const renderExecutionFlow = () => {
    if (group.execution_mode === 'sequential') {
      return (
        <FlowContainer mode="sequential">
          {group.cards.map((card, index) => (
            <React.Fragment key={card.id}>
              <FlowCard>
                <div>{card.icon}</div>
                <div>{card.name}</div>
              </FlowCard>
              {index < group.cards.length - 1 && (
                <FlowArrow>�?/FlowArrow>
              )}
            </React.Fragment>
          ))}
        </FlowContainer>
      )
    } else {
      return (
        <FlowContainer mode="parallel">
          {group.cards.map(card => (
            <FlowCard key={card.id}>
              <div>{card.icon}</div>
              <div>{card.name}</div>
            </FlowCard>
          ))}
          <ParallelHub />
        </FlowContainer>
      )
    }
  }

  return (
    <GroupContainer
      status={group.status}
      size={size}
      className={`strategy-group ${className}`}
      onClick={() => setIsExpanded(!isExpanded)}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      layout
    >
      <GroupHeader>
        <GroupTitleSection>
          <GroupTitle>{group.name}</GroupTitle>
          <GroupSubtitle>
            <StatusBadge className="group-status">
              {getStatusIcon(group.status)} {getStatusText(group.status)}
            </StatusBadge>
            <ExecutionModeIndicator mode={group.execution_mode}>
              {group.execution_mode === 'sequential' ? '串行执行' : '并行执行'}
            </ExecutionModeIndicator>
            <span>{group.cards.length}个策�?/span>
          </GroupSubtitle>
        </GroupTitleSection>
      </GroupHeader>

      {renderExecutionFlow()}

      <MetricsContainer>
        <Metric positive={group.performance_metrics.total_return > 0}>
          <span className="metric-value">
            {formatPercentage(group.performance_metrics.total_return)}
          </span>
          <span className="metric-label">总收�?/span>
        </Metric>
        <Metric>
          <span className="metric-value">
            {group.performance_metrics.sharpe_ratio.toFixed(2)}
          </span>
          <span className="metric-label">夏普比率</span>
        </Metric>
        <Metric positive={false}>
          <span className="metric-value">
            {formatPercentage(group.performance_metrics.max_drawdown)}
          </span>
          <span className="metric-label">最大回�?/span>
        </Metric>
        <Metric positive={group.performance_metrics.win_rate > 0.5}>
          <span className="metric-value">
            {formatPercentage(group.performance_metrics.win_rate)}
          </span>
          <span className="metric-label">胜率</span>
        </Metric>
      </MetricsContainer>

      <ActionsContainer>
        <div style={{ display: 'flex', gap: '8px' }}>
          <ActionButton 
            onClick={(e) => { e.stopPropagation(); onEdit?.(group) }}
            title="编辑策略�?
          >
            ⚙️
          </ActionButton>
          
          {group.status === 'active' ? (
            <ActionButton 
              onClick={(e) => { e.stopPropagation(); onStop?.(group) }}
              title="停止执行"
            >
              ⏹️
            </ActionButton>
          ) : (
            <ActionButton 
              primary
              onClick={(e) => { e.stopPropagation(); onExecute?.(group) }}
              title="开始执�?
            >
              🚀
            </ActionButton>
          )}
          
          <ActionButton 
            onClick={(e) => { e.stopPropagation(); /* 查看详情 */ }}
            title="查看详情"
          >
            📊
          </ActionButton>
        </div>
        
        <ActionButton 
          danger
          onClick={(e) => { e.stopPropagation(); onDelete?.(group) }}
          title="删除策略�?
        >
          🗑�?        </ActionButton>
      </ActionsContainer>
    </GroupContainer>
  )
}

// 🎨 策略组展示示�?export const StrategyGroupShowcase: React.FC = () => {
  const sampleGroups: StrategyGroupCardData[] = [
    {
      id: '1',
      name: '多因子价值投�?,
      description: '基于多个财务指标的价值投资策�?,
      status: 'active',
      execution_mode: 'sequential',
      cards: [
        { id: '1', name: 'PE筛�?, icon: '📈', level: 3, power: 78, defense: 65 },
        { id: '2', name: 'ROE过滤', icon: '📊', level: 4, power: 85, defense: 78 },
        { id: '3', name: '成长性评�?, icon: '🔍', level: 3, power: 72, defense: 68 }
      ],
      performance_metrics: {
        total_return: 0.156,
        sharpe_ratio: 1.24,
        max_drawdown: -0.08,
        win_rate: 0.67
      },
      created_at: '2024-01-15'
    },
    {
      id: '2',
      name: '短线量化交易',
      description: '高频交易策略组合',
      status: 'paused',
      execution_mode: 'parallel',
      cards: [
        { id: '4', name: 'KDJ信号', icon: '�?, level: 2, power: 65, defense: 45 },
        { id: '5', name: '成交量异�?, icon: '📶', level: 3, power: 71, defense: 52 },
        { id: '6', name: '动量跟踪', icon: '🎯', level: 2, power: 58, defense: 48 }
      ],
      performance_metrics: {
        total_return: 0.087,
        sharpe_ratio: 0.89,
        max_drawdown: -0.12,
        win_rate: 0.58
      },
      created_at: '2024-02-01'
    }
  ]

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px', 
      padding: '20px',
      background: 'linear-gradient(135deg, #000008 0%, #0A0A0F 100%)',
      minHeight: '100vh'
    }}>
      {sampleGroups.map(group => (
        <StrategyGroupCard
          key={group.id}
          group={group}
          onEdit={(group) => console.log('Edit:', group.name)}
          onExecute={(group) => console.log('Execute:', group.name)}
          onStop={(group) => console.log('Stop:', group.name)}
          onDelete={(group) => console.log('Delete:', group.name)}
        />
      ))}
    </div>
  )
}

export default StrategyGroupCard
