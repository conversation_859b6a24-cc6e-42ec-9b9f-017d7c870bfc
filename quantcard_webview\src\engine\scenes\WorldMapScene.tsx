import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Stars, Text, Sphere } from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import { useUIState } from '../../store/hooks';
import { useAuth } from '../../store/hooks/useAuth';
import * as THREE from 'three';
import GameNotification from '../components/GameNotification';
import type { NotificationData } from '../components/GameNotification';
import InventoryDrawer from '../components/InventoryDrawer';
import type { UnifiedCardData } from '../components/UniversalCard';
import LoginModal from '../components/auth/LoginModal';
import { useWebSocket } from '../../services/WebSocketProvider';
import { unifiedWebSocket, MessageType } from '../../services/unifiedWebSocket';
import './WorldMapScene.scss';

// 地标数据接口
interface Landmark {
  id: string;
  name: string;
  description: string;
  type: 'museum' | 'exchange' | 'tower' | 'center' | 'adventure';
  position: [number, number, number]; // 3D 坐标
  icon: string;
  color: string;
  unlocked: boolean;
}

/**
 * 3D 地标组件 - 垂直于地球表面定向
 */
const LandmarkMarker = React.memo(({ landmark, onSelect, selected }: {
  landmark: Landmark;
  onSelect: (landmark: Landmark) => void;
  selected: boolean;
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const groupRef = useRef<THREE.Group>(null);
  const [hovered, setHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current && groupRef.current) {
      // 悬浮动画 - 相对于地标本地位置
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 2) * 0.1;

      // 旋转动画
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;

      // 选中或悬停时放大
      const targetScale = (selected || hovered) ? 1.5 : 1;
      meshRef.current.scale.lerp(new THREE.Vector3(targetScale, targetScale, targetScale), 0.1);

      // 计算地标在球面上的方向向量，使地标垂直于地球表面
      const position = new THREE.Vector3(...landmark.position);
      position.normalize(); // 归一化得到从地心指向地标位置的方向向量

      // 设置组的朝向，使地标垂直于地球表面
      groupRef.current.lookAt(
        groupRef.current.position.x + position.x,
        groupRef.current.position.y + position.y,
        groupRef.current.position.z + position.z
      );
    }
  });

  const handleClick = useCallback(() => {
    if (landmark.unlocked) {
      onSelect(landmark);
    }
  }, [landmark, onSelect]);

  return (
    <group ref={groupRef} position={landmark.position}>
      {/* 地标主体 */}
      <mesh
        ref={meshRef}
        onClick={handleClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        <cylinderGeometry args={[0.3, 0.5, 1, 8]} />
        <meshStandardMaterial
          color={landmark.unlocked ? landmark.color : '#666666'}
          emissive={landmark.unlocked ? landmark.color : '#000000'}
          emissiveIntensity={selected || hovered ? 0.6 : 0.3}
          transparent
          opacity={landmark.unlocked ? 1 : 0.5}
        />
      </mesh>

      {/* 标签 - 始终面向相机 */}
      <Text
        position={[0, 1.5, 0]}
        fontSize={0.2}
        color={landmark.unlocked ? '#ffffff' : '#888888'}
        anchorX="center"
        anchorY="bottom"
      >
        {landmark.name}
      </Text>

      {/* 发光效果 */}
      {(selected || hovered) && landmark.unlocked && (
        <Sphere args={[0.8]} position={[0, 0, 0]}>
          <meshBasicMaterial
            color={landmark.color}
            transparent
            opacity={0.2}
            side={THREE.DoubleSide}
          />
        </Sphere>
      )}
    </group>
  );
});

/**
 * 3D 地球组件
 */
const Earth3D = React.memo(({ landmarks, onLandmarkSelect, selectedLandmark }: {
  landmarks: Landmark[];
  onLandmarkSelect: (landmark: Landmark) => void;
  selectedLandmark: Landmark | null;
}) => {
  const earthRef = useRef<THREE.Mesh>(null);

  useFrame(() => {
    if (earthRef.current) {
      earthRef.current.rotation.y += 0.002; // 缓慢自转
    }
  });

  return (
    <>
      {/* 星空背景 */}
      <Stars
        radius={300}
        depth={50}
        count={5000}
        factor={4}
        saturation={0}
        fade
        speed={0.5}
      />

      {/* 环境光 */}
      <ambientLight intensity={0.4} color="#f0f8ff" />

      {/* 主光源 */}
      <directionalLight
        position={[10, 10, 5]}
        intensity={1.2}
        color="#ffffff"
        castShadow
        shadow-mapSize={[2048, 2048]}
      />

      {/* 补光 */}
      <pointLight
        position={[-10, -10, -5]}
        intensity={0.3}
        color="#87ceeb"
      />

      {/* 地球 */}
      <mesh ref={earthRef} receiveShadow>
        <icosahedronGeometry args={[4, 2]} />
        <meshPhongMaterial
          color="#e6f3ff"
          transparent
          opacity={0.9}
          shininess={100}
          wireframe={false}
        />
      </mesh>

      {/* 地球线框 */}
      <mesh>
        <icosahedronGeometry args={[4.05, 2]} />
        <meshBasicMaterial
          color="#3b82f6"
          wireframe
          transparent
          opacity={0.3}
        />
      </mesh>

      {/* 地标 */}
      {landmarks.map((landmark) => (
        <LandmarkMarker
          key={landmark.id}
          landmark={landmark}
          onSelect={onLandmarkSelect}
          selected={selectedLandmark?.id === landmark.id}
        />
      ))}
    </>
  );
});

/**
 * 3D 世界地图场景 - 浅色主题风格
 */
const WorldMapScene = React.memo(() => {
  const { switchScene } = useUIState();
  const { user, isAuthenticated, isGuest } = useAuth();
  const { connected: wsConnected, error: wsError } = useWebSocket();
  const [selectedLandmark, setSelectedLandmark] = useState<Landmark | null>(null);
  const [showWelcome, setShowWelcome] = useState(true);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isInventoryOpen, setIsInventoryOpen] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  // 通知系统函数
  const addNotification = useCallback((notification: Omit<NotificationData, 'id'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
    setNotifications(prev => [...prev, newNotification]);

    // 自动移除通知
    setTimeout(() => {
      removeNotification(newNotification.id);
    }, notification.duration || 4000);
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // 地标数据 - 球面分布，使用 useMemo 缓存
  const landmarks = useMemo<Landmark[]>(() => [
    {
      id: 'museum',
      name: '策略博物馆',
      description: '学习和收藏策略卡牌，探索量化交易的知识宝库',
      type: 'museum',
      position: [4.2, 1.5, 0], // 球面上的位置
      icon: '🏛️',
      color: '#8b5cf6',
      unlocked: true
    },
    {
      id: 'exchange',
      name: '全球交易所',
      description: '实时市场数据、交易执行和行情分析',
      type: 'exchange',
      position: [-2.8, 2.5, 2.2],
      icon: '🏢',
      color: '#10b981',
      unlocked: true
    },
    {
      id: 'macro_tower',
      name: '宏观分析塔',
      description: '宏观经济数据、政策分析和市场预测',
      type: 'tower',
      position: [1.2, -1.8, -3.8],
      icon: '🗼',
      color: '#f59e0b',
      unlocked: true
    },
    {
      id: 'control_center',
      name: '策略控制中心',
      description: '策略编辑、回测分析和组合优化',
      type: 'center',
      position: [-3.2, -0.5, -2.5],
      icon: '🏠',
      color: '#3b82f6',
      unlocked: true
    },
    {
      id: 'adventure_portal',
      name: '策略冒险之门',
      description: '闯关挑战、技能训练和成就系统',
      type: 'adventure',
      position: [0.8, 3.2, -2.8],
      icon: '⚔️',
      color: '#ef4444',
      unlocked: true
    }
  ], []);

  // 组件挂载时显示欢迎消息
  useEffect(() => {
    // 3秒后隐藏欢迎消息
    const timer = setTimeout(() => {
      setShowWelcome(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  // WebSocket状态监听，显示连接状态通知（防抖处理）
  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (wsConnected) {
      // 防抖：延迟显示成功通知，避免频繁重连时的重复提示
      timer = setTimeout(() => {
        addNotification({
          type: 'success',
          title: '连接成功',
          message: '实时功能已启用',
          icon: '🔗',
          duration: 2000
        });
      }, 1000);
    } else if (wsError) {
      // 错误立即显示
      addNotification({
        type: 'warning',
        title: '连接异常',
        message: '无法建立实时连接，部分功能可能受限',
        icon: '⚠️',
        duration: 5000
      });
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [wsConnected, wsError, addNotification])

  /**
   * 处理地标选择 - 简化版本，权限控制在路由层处理
   */
  const handleLandmarkSelect = useCallback((landmark: Landmark) => {
    setSelectedLandmark(landmark);

    // 显示选择通知
    addNotification({
      type: 'info',
      title: '地标已选择',
      message: `正在前往 ${landmark.name}...`,
      icon: landmark.icon,
      duration: 2000
    });

    // 根据地标类型跳转到相应场景
    setTimeout(() => {
      switch (landmark.type) {
        case 'museum':
          addNotification({
            type: 'success',
            title: '传送成功',
            message: '欢迎来到策略博物馆！',
            icon: '✨'
          });
          switchScene('Codex');
          break;
        case 'exchange':
          addNotification({
            type: 'info',
            title: '交易大厅',
            message: '策略卡牌交易即将开放',
            icon: '💱'
          });
          break;
        case 'tower':
          addNotification({
            type: 'info',
            title: '分析之塔',
            message: '宏观分析功能即将开放',
            icon: '🏗️'
          });
          break;
        case 'center':
          addNotification({
            type: 'success',
            title: '策略中心',
            message: '进入策略优化中心',
            icon: '🎯'
          });
          switchScene('StrategyOptimization');
          break;
        case 'adventure':
          addNotification({
            type: 'warning',
            title: '冒险模式',
            message: '准备好接受挑战了吗？',
            icon: '⚔️'
          });
          switchScene('Adventure');
          break;
      }
    }, 1500);
  }, [switchScene, addNotification]);

  /**
   * 处理背包按钮点击 - 简化版本
   */
  const handleInventoryClick = useCallback(() => {
    setIsInventoryOpen(true);
    addNotification({
      type: 'info',
      title: '打开背包',
      message: '查看你的策略卡牌收藏',
      icon: '🎒',
      duration: 2000
    });
  }, [addNotification]);



  /**
   * 处理卡牌选择
   */
  const handleCardSelect = useCallback((card: UnifiedCardData) => {
    addNotification({
      type: 'success',
      title: '卡牌选择',
      message: `选择了卡牌`,
      icon: '🃏',
      duration: 2000
    });
    // 可以在这里添加卡牌使用逻辑
  }, [addNotification]);

  return (
    <div className="world-map-scene light-theme">
      {/* 欢迎消息 */}
      <AnimatePresence>
        {showWelcome && (
          <motion.div
            className="welcome-banner"
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.8 }}
          >
            <div className="banner-content">
              <h1 className="banner-title">🌍 欢迎来到 QuantCard 世界</h1>
              <p className="banner-subtitle">点击地标探索不同的功能区域</p>
            </div>
            <button
              className="banner-close"
              onClick={() => setShowWelcome(false)}
            >
              ✕
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 用户头像区域 - 增强游戏化设计 */}
                  <div className="user-panel" onClick={() => switchScene('Profile')}>
        <div className="user-content">
          <div className="user-avatar">
            {user ? '👤' : '🔐'}
          </div>
          <div className="user-name">
            {user ? (user.username || 'Trader') : '未登录'}
          </div>
          <div className="user-level">
            {user ? 'Lv.15' : '点击登录'}
          </div>
        </div>
        <div className="user-info-panel">
          <div className="user-badge">
            <div className="title">⚡ 盟主</div>
            <div className="score">💰 1,280</div>
            <div className="gems">💎 856</div>
          </div>
          <div className="exp-bar">
            <div className="exp-fill" style={{ width: '65%' }}></div>
            <div className="exp-text">2,580/4,000 EXP</div>
          </div>
        </div>
      </div>

      {/* 左侧功能按钮组 - 纵向排列圆形按钮 */}
      <div className="left-actions">
        <button
          className="action-btn circular"
          onClick={() => {
            // 排行榜功能
            addNotification({
              type: 'info',
              title: '排行榜',
              message: '查看策略排行榜',
              icon: '🏆'
            });
          }}
          title="排行榜"
        >
          <div className="btn-icon">🏆</div>
          <div className="btn-label">排行</div>
        </button>
        <button
          className="action-btn circular"
          onClick={async () => {
            if (!isAuthenticated || isGuest) {
              // 未登录用户提示需要登录
              addNotification({
                type: 'warning',
                title: '需要登录',
                message: '请先登录才能使用抽卡功能',
                icon: '🔐'
              });
              setShowLoginModal(true);
              return;
            }

            // 跳转到抽卡场景
            switchScene('CardDraw');
          }}
          title="策略抽卡"
        >
          <div className="btn-icon">🎴</div>
          <div className="btn-label">抽卡</div>
        </button>
        <button
          className="action-btn circular"
          onClick={() => {
            // 公告功能
            addNotification({
              type: 'warning',
              title: '系统公告',
              message: '查看最新公告和更新',
              icon: '📢'
            });
          }}
          title="系统公告"
        >
          <div className="btn-icon">📢</div>
          <div className="btn-label">公告</div>
        </button>
        <button
          className="action-btn circular"
          onClick={() => switchScene('Adventure')}
          title="策略闯关"
        >
          <div className="btn-icon">⚔️</div>
          <div className="btn-label">闯关</div>
        </button>
      </div>

      {/* 全屏按钮 - 右上角灰色平面化 */}
      <button
        className="fullscreen-btn"
        onClick={() => {
          if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
          } else {
            document.exitFullscreen();
          }
        }}
        title="全屏模式"
      >
        📱
      </button>

      {/* 底部操作栏 - 游戏化设计 */}
      <div className="bottom-actions">
        <button 
          className="bottom-btn secondary"
          onClick={() => switchScene('StrategyOptimization')}
        >
          <div className="btn-icon">🎮</div>
          <div className="btn-label">控制台</div>
        </button>
        <button 
          className="bottom-btn secondary"
          onClick={() => switchScene('Backtest')}
        >
          <div className="btn-icon">📈</div>
          <div className="btn-label">回测</div>
        </button>
        <button
          className="bottom-btn primary create-strategy-btn"
          onClick={() => switchScene('StrategyCreation')}
        >
          <div className="btn-icon plus-icon">+</div>
          <div className="btn-label">创建策略</div>
        </button>
        <button
          className="bottom-btn secondary"
          onClick={handleInventoryClick}
        >
          <div className="btn-icon">🎒</div>
          <div className="btn-label">背包</div>
        </button>
        <button className="bottom-btn secondary">
          <div className="btn-icon">👥</div>
          <div className="btn-label">关注</div>
        </button>
      </div>

      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 2, 8], fov: 75 }}
        style={{ width: '100%', height: '100%' }}
        className="world-map-canvas"
      >
        <Earth3D
          landmarks={landmarks}
          onLandmarkSelect={handleLandmarkSelect}
          selectedLandmark={selectedLandmark}
        />

        {/* 相机控制 */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={6}
          maxDistance={15}
          minPolarAngle={Math.PI / 4}
          maxPolarAngle={Math.PI - Math.PI / 8}
          autoRotate={!selectedLandmark}
          autoRotateSpeed={0.3}
          target={[0, 0, 0]}
        />
      </Canvas>

      {/* 地标详情面板 */}
      <AnimatePresence>
        {selectedLandmark && (
          <motion.div
            className="landmark-panel"
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.4 }}
          >
            <div className="panel-header">
              <div className="landmark-icon">{selectedLandmark.icon}</div>
              <div className="landmark-info">
                <h3 className="landmark-name">{selectedLandmark.name}</h3>
              </div>
              <button
                className="panel-close"
                onClick={() => setSelectedLandmark(null)}
              >
                ✕
              </button>
            </div>

            <div className="panel-content">
              <p className="landmark-description">{selectedLandmark.description}</p>

              <button
                className="enter-btn"
                onClick={() => handleLandmarkSelect(selectedLandmark)}
              >
                进入 {selectedLandmark.name} →
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 游戏化通知系统 */}
      <GameNotification
        notifications={notifications}
        onDismiss={removeNotification}
      />

      {/* 抽屉式背包 */}
      <InventoryDrawer
        isOpen={isInventoryOpen}
        onClose={() => setIsInventoryOpen(false)}
        onCardSelect={handleCardSelect}
      />

      {/* 登录模态框 */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSuccess={() => {
          addNotification({
            type: 'success',
            title: '登录成功',
            message: '欢迎回来！现在可以体验完整功能',
            icon: '🎉',
            duration: 3000
          });
        }}
        title="登录 QuantCard"
        subtitle="登录后可以使用完整的量化策略功能"
        showGuestOption={true}
      />
    </div>
  );
});

WorldMapScene.displayName = 'WorldMapScene';

export default WorldMapScene;