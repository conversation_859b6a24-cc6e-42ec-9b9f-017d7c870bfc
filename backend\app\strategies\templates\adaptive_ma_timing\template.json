{"id": "adaptive_ma_timing", "name": "自适应均线择时策略", "description": "基于多重指数平滑均线的智能择时策略，具备自适应周期调整、趋势强度确认和动态止损功能", "version": "1.0.0", "author": "QuantCard团队", "tags": ["技术分析", "均线系统", "自适应算法", "择时策略", "趋势跟踪", "动态止损"], "createTime": "2024-01-01T00:00:00Z", "updateTime": "2024-01-01T00:00:00Z", "parameters": {"ma_type": {"label": "均线类型", "type": "select", "default": "EMA", "options": [{"value": "SMA", "label": "简单移动平均线"}, {"value": "EMA", "label": "指数移动平均线"}, {"value": "DEMA", "label": "双重指数移动平均线"}, {"value": "TEMA", "label": "三重指数移动平均线"}], "required": true, "description": "不同类型的均线对价格变化的敏感度不同，EMA最常用", "category": "均线设置"}, "base_period": {"label": "基础均线周期", "type": "number", "default": 20, "min": 5, "max": 100, "step": 1, "required": true, "placeholder": "请输入基础均线周期", "description": "快线周期，慢线自动设置为2倍周期，建议10-30", "unit": "分钟", "category": "均线设置"}, "adaptive_enabled": {"label": "启用自适应调整", "type": "switch", "default": true, "required": true, "description": "根据市场波动率自动调整均线周期，提高策略适应性", "category": "自适应设置"}, "volatility_lookback": {"label": "波动率回溯期", "type": "number", "default": 20, "min": 10, "max": 50, "step": 1, "required": true, "placeholder": "请输入波动率回溯期", "description": "用于计算市场波动率的历史周期数", "unit": "分钟", "category": "自适应设置"}, "min_adx": {"label": "最小ADX趋势强度", "type": "number", "default": 20.0, "min": 10.0, "max": 50.0, "step": 1.0, "required": true, "placeholder": "请输入最小ADX值", "description": "ADX低于此值认为趋势不明确，建议20以上", "unit": "", "category": "信号过滤"}, "min_volume_ratio": {"label": "最小成交量倍数", "type": "number", "default": 1.2, "min": 1.0, "max": 3.0, "step": 0.1, "required": true, "placeholder": "请输入最小成交量倍数", "description": "成交量须超过均量的倍数才确认信号，建议1.2以上", "unit": "倍", "category": "信号过滤"}, "confirmation_periods": {"label": "信号确认周期", "type": "number", "default": 2, "min": 1, "max": 5, "step": 1, "required": true, "placeholder": "请输入信号确认周期", "description": "信号持续确认的周期数，减少假突破", "unit": "分钟", "category": "信号过滤"}}, "outputs": [{"name": "timing_signals", "type": "signal", "description": "择时交易信号", "fields": ["symbol", "name", "direction", "confidence", "trigger_condition", "current_price", "ma_fast", "ma_slow", "adx", "trend_strength", "stop_loss", "atr", "volume_ratio"]}], "modes": [{"name": "timing", "label": "择时模式", "description": "实时监控均线信号，生成买卖交易信号"}, {"name": "backtest", "label": "回测模式", "description": "使用历史数据验证策略效果"}, {"name": "monitor", "label": "监控模式", "description": "监控市场状态，发出趋势变化提醒"}], "supportedDataSources": ["questdb"], "examples": [{"name": "经典EMA策略", "description": "使用EMA均线，适合大多数市场环境", "parameters": {"ma_type": "EMA", "base_period": 20, "adaptive_enabled": true, "volatility_lookback": 20, "min_adx": 20.0, "min_volume_ratio": 1.2, "confirmation_periods": 2}}, {"name": "快速TEMA策略", "description": "使用TEMA均线，对价格变化更敏感", "parameters": {"ma_type": "TEMA", "base_period": 15, "adaptive_enabled": true, "volatility_lookback": 15, "min_adx": 25.0, "min_volume_ratio": 1.5, "confirmation_periods": 1}}, {"name": "稳健DEMA策略", "description": "使用DEMA均线，平衡灵敏度和稳定性", "parameters": {"ma_type": "DEMA", "base_period": 25, "adaptive_enabled": true, "volatility_lookback": 25, "min_adx": 18.0, "min_volume_ratio": 1.1, "confirmation_periods": 3}}], "template_id": "timing", "ui": {"icon": "lineChart", "color": "#1890ff", "group": "技术分析", "order": 2, "form": {"layout": "vertical", "compact": false, "showDescription": true, "categories": [{"name": "均线设置", "label": "均线基础设置", "collapsed": false}, {"name": "自适应设置", "label": "自适应功能", "collapsed": false}, {"name": "信号过滤", "label": "信号过滤条件", "collapsed": true}]}, "chart": {"type": "candlestick", "indicators": [{"type": "ma", "params": ["ma_fast", "ma_slow"], "colors": ["#ff4d4f", "#52c41a"]}, {"type": "adx", "params": ["adx"], "subChart": true}, {"type": "volume", "params": ["volume", "volume_ratio"], "subChart": true}], "signals": {"buy": {"color": "#52c41a", "shape": "triangle-up"}, "sell": {"color": "#ff4d4f", "shape": "triangle-down"}}}}, "performance": {"complexity": "high", "dataIntensive": true, "cacheEnabled": true, "cacheTtl": 300, "realtime": true}, "risk": {"level": "medium", "factors": ["技术指标存在滞后性", "震荡市场容易产生假信号", "需要结合止损管理风险", "自适应调整可能过于频繁"], "suggestions": ["建议在趋势明确的市场使用", "严格执行止损策略", "避免在重要消息发布前后使用"]}, "algorithm": {"type": "technical_analysis", "complexity": "advanced", "features": ["多类型移动平均线", "自适应周期调整", "ADX趋势强度确认", "成交量确认机制", "ATR动态止损", "假突破过滤"]}}