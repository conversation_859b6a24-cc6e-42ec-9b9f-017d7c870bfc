"""
API v1版本路由入口
整合所有API端点，提供统一的路由注册
"""
from fastapi import APIRouter

# 导入所有API端点模块
from .endpoints import strategies, market_data, auth, users, monitor, backtest, inventory, card_system

# 创建v1版本API路由器
api_router = APIRouter()

# 注册认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

# 注册用户相关路由
api_router.include_router(users.router, prefix="/users", tags=["users"])

# 注册策略相关路由
api_router.include_router(strategies.router, prefix="/strategies", tags=["strategies"])

# 注册市场数据相关路由
api_router.include_router(market_data.router, prefix="/market-data", tags=["market-data"])

# 注册回测相关路由
api_router.include_router(backtest.router, prefix="/backtests", tags=["backtests"])

# 注册监控相关路由
api_router.include_router(monitor.router, prefix="/monitor", tags=["monitor"])

# 注册库存相关路由
api_router.include_router(inventory.router, prefix="/inventory", tags=["inventory"])

# 注册卡牌系统相关路由
api_router.include_router(card_system.router, prefix="/cards", tags=["cards"]) 