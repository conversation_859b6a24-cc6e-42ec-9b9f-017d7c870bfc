/**
 * 📚 策略图鉴面板 - 展示所有策略卡牌收集状态
 * 支持筛选、搜索、详情查看等功能
 */

import React, { useState, useMemo, useEffect } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import UnifiedMobileNav from '../ui/Nav/UnifiedMobileNav'
import { useAuth } from '../../../store/hooks/useAuth'
import { unifiedWebSocket } from '../../../services/unifiedWebSocket'

// 类型定义
interface CardTemplate {
  template_id: string
  name: string
  description: string
  tags: string[]
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic'
  icon: string
  color: string
  category: string
  owned: boolean
  owned_count: number
  total_acquired: number
  first_acquired?: string
}

interface CodexStats {
  total_cards: number
  owned_cards: number
  collection_rate: number
  total_owned_count: number
}

// 样式组件
const Container = styled.div`
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
`

const Content = styled.div`
  padding: 1rem;
  height: calc(100vh - 140px);
  overflow-y: auto;
`

const StatsBar = styled.div`
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`

const StatItem = styled.div`
  text-align: center;
  color: white;
  
  .value {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
  }
  
  .label {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 0.25rem;
  }
`

const FilterBar = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
`

const FilterButton = styled(motion.button)<{ $active: boolean }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  background: ${props => props.$active 
    ? 'rgba(255, 255, 255, 0.9)' 
    : 'rgba(255, 255, 255, 0.2)'};
  color: ${props => props.$active ? '#333' : 'white'};
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
`

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  margin-bottom: 1rem;
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
  
  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
  }
`

const CardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`

const CardItem = styled(motion.div)<{ $owned: boolean; $rarity: string }>`
  background: ${props => props.$owned 
    ? 'rgba(255, 255, 255, 0.15)' 
    : 'rgba(255, 255, 255, 0.05)'};
  border: 2px solid ${props => {
    const rarityColors = {
      common: '#9CA3AF',
      rare: '#3B82F6', 
      epic: '#8B5CF6',
      legendary: '#F59E0B',
      mythic: '#EF4444'
    }
    return rarityColors[props.$rarity as keyof typeof rarityColors] || '#9CA3AF'
  }};
  border-radius: 16px;
  padding: 1rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => {
      const rarityColors = {
        common: '#9CA3AF',
        rare: '#3B82F6', 
        epic: '#8B5CF6',
        legendary: '#F59E0B',
        mythic: '#EF4444'
      }
      return rarityColors[props.$rarity as keyof typeof rarityColors] || '#9CA3AF'
    }};
  }
  
  filter: ${props => props.$owned ? 'none' : 'grayscale(70%)'};
  opacity: ${props => props.$owned ? 1 : 0.6};
`

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
`

const CardIcon = styled.div`
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
`

const CardInfo = styled.div`
  flex: 1;
  color: white;
  
  .name {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
  }
  
  .rarity {
    font-size: 0.8rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
`

const CardDescription = styled.div`
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
`

const CardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
`

const OwnedBadge = styled.div<{ $count: number }>`
  background: ${props => props.$count > 0 
    ? 'linear-gradient(45deg, #10B981, #059669)' 
    : 'rgba(255, 255, 255, 0.1)'};
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
`

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: white;
  font-size: 1.1rem;
`

const ErrorMessage = styled.div`
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #FCA5A5;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 1rem;
`

type FilterKey = 'all' | 'owned' | 'unowned' | 'timing' | 'filter' | 'risk_management' | 'common' | 'rare' | 'epic' | 'legendary'

export const CodexPanel: React.FC = () => {
  const [search, setSearch] = useState('')
  const [filter, setFilter] = useState<FilterKey>('all')
  const [detailId, setDetailId] = useState<string | null>(null)
  const [codexItems, setCodexItems] = useState<CardTemplate[]>([])
  const [stats, setStats] = useState<CodexStats>({
    total_cards: 0,
    owned_cards: 0,
    collection_rate: 0,
    total_owned_count: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { isAuthenticated, isGuest } = useAuth()

  // 加载卡牌图鉴数据
  const loadCodexData = async () => {
    if (!isAuthenticated || isGuest) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // 通过统一API获取卡牌图鉴
      const response = await fetch('/api/cards/codex', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.success) {
        setCodexItems(result.data.items)
        setStats(result.data.stats)
      } else {
        throw new Error(result.message || '获取图鉴数据失败')
      }
    } catch (err) {
      console.error('加载图鉴数据失败:', err)
      setError(err instanceof Error ? err.message : '加载失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadCodexData()
  }, [isAuthenticated, isGuest])

  // 监听WebSocket库存更新
  useEffect(() => {
    if (!isAuthenticated || isGuest) return

    const unsubscribe = unifiedWebSocket.subscribe('inventory', (message) => {
      if (message.subtype === 'update') {
        console.log('收到库存更新，刷新图鉴数据')
        loadCodexData()
      }
    })

    return unsubscribe
  }, [isAuthenticated, isGuest])

  // 过滤和搜索逻辑
  const filteredItems = useMemo(() => {
    let result = codexItems

    // 应用搜索
    if (search.trim()) {
      const searchLower = search.toLowerCase()
      result = result.filter(item => 
        item.name.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchLower))
      )
    }

    // 应用筛选
    if (filter !== 'all') {
      if (filter === 'owned') {
        result = result.filter(item => item.owned)
      } else if (filter === 'unowned') {
        result = result.filter(item => !item.owned)
      } else {
        result = result.filter(item => 
          item.category === filter || item.rarity === filter
        )
      }
    }

    return result
  }, [codexItems, search, filter])

  const rarityDisplayNames = {
    common: '普通',
    rare: '稀有', 
    epic: '史诗',
    legendary: '传说',
    mythic: '神话'
  }

  const categoryDisplayNames = {
    timing: '择时',
    filter: '选股',
    risk_management: '风控'
  }

  if (!isAuthenticated) {
    return (
      <Container>
        <UnifiedMobileNav title="策略图鉴" showBackButton={true}>
          <Content>
            <div style={{ textAlign: 'center', color: 'white', marginTop: '2rem' }}>
              <h3>需要登录才能查看图鉴</h3>
              <p>登录后可以查看所有策略卡牌的收集状态</p>
            </div>
          </Content>
        </UnifiedMobileNav>
      </Container>
    )
  }

  if (isGuest) {
    return (
      <Container>
        <UnifiedMobileNav title="策略图鉴" showBackButton={true}>
          <Content>
            <div style={{ textAlign: 'center', color: 'white', marginTop: '2rem' }}>
              <h3>访客模式下无法查看图鉴</h3>
              <p>请注册账号后体验完整功能</p>
            </div>
          </Content>
        </UnifiedMobileNav>
      </Container>
    )
  }

  return (
    <Container>
      <UnifiedMobileNav title="策略图鉴" showBackButton={true}>
        <Content>
          {/* 统计信息 */}
          <StatsBar>
            <StatItem>
              <div className="value">{stats.total_cards}</div>
              <div className="label">总卡牌</div>
            </StatItem>
            <StatItem>
              <div className="value">{stats.owned_cards}</div>
              <div className="label">已收集</div>
            </StatItem>
            <StatItem>
              <div className="value">{stats.collection_rate}%</div>
              <div className="label">收集率</div>
            </StatItem>
            <StatItem>
              <div className="value">{stats.total_owned_count}</div>
              <div className="label">总数量</div>
            </StatItem>
          </StatsBar>

          {/* 搜索框 */}
          <SearchInput
            type="text"
            placeholder="搜索策略卡牌..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />

          {/* 筛选按钮 */}
          <FilterBar>
            {[
              { key: 'all', label: '全部' },
              { key: 'owned', label: '已拥有' },
              { key: 'unowned', label: '未拥有' },
              { key: 'timing', label: '择时' },
              { key: 'filter', label: '选股' },
              { key: 'risk_management', label: '风控' },
              { key: 'common', label: '普通' },
              { key: 'rare', label: '稀有' },
              { key: 'epic', label: '史诗' },
              { key: 'legendary', label: '传说' }
            ].map(({ key, label }) => (
              <FilterButton
                key={key}
                $active={filter === key}
                onClick={() => setFilter(key as FilterKey)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {label}
              </FilterButton>
            ))}
          </FilterBar>

          {/* 错误提示 */}
          {error && (
            <ErrorMessage>
              {error}
                  <button
                onClick={loadCodexData}
                    style={{
                  marginLeft: '1rem', 
                  background: 'none', 
                  border: '1px solid #FCA5A5',
                  color: '#FCA5A5',
                  padding: '0.25rem 0.5rem',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                重试
                  </button>
            </ErrorMessage>
          )}

          {/* 加载状态 */}
          {loading && (
            <LoadingSpinner>
              <div>正在加载图鉴数据...</div>
            </LoadingSpinner>
          )}

          {/* 卡牌网格 */}
          {!loading && !error && (
            <CardGrid>
              {filteredItems.map((item) => (
                <CardItem
                  key={item.template_id}
                  $owned={item.owned}
                  $rarity={item.rarity}
                  onClick={() => setDetailId(item.template_id)}
                  whileHover={{ scale: 1.02, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <CardHeader>
                    <CardIcon>{item.icon}</CardIcon>
                    <CardInfo>
                      <div className="name">{item.name}</div>
                      <div className="rarity">
                        {rarityDisplayNames[item.rarity]} • {categoryDisplayNames[item.category as keyof typeof categoryDisplayNames] || item.category}
                </div>
                    </CardInfo>
                  </CardHeader>
                  
                  <CardDescription>
                    {item.description}
                  </CardDescription>
                  
                  <CardFooter>
                    <OwnedBadge $count={item.owned_count}>
                      {item.owned ? `拥有 ${item.owned_count}` : '未拥有'}
                    </OwnedBadge>
                    {item.first_acquired && (
                      <span>
                        首次获得: {new Date(item.first_acquired).toLocaleDateString()}
                      </span>
                    )}
                  </CardFooter>
                </CardItem>
              ))}
            </CardGrid>
          )}

          {/* 空状态 */}
          {!loading && !error && filteredItems.length === 0 && (
            <div style={{ 
              textAlign: 'center', 
              color: 'rgba(255, 255, 255, 0.6)',
              marginTop: '2rem' 
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📚</div>
              <h3>暂无匹配的卡牌</h3>
              <p>尝试调整搜索条件或筛选设置</p>
              </div>
          )}
        </Content>
    </UnifiedMobileNav>
    </Container>
  )
}

export default CodexPanel 
