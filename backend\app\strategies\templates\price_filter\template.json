{"template_id": "price_filter", "name": "股价", "description": "根据设定的价格条件筛选股票，支持区间范围筛选   <span style='color: #808080'>例：价格介于10元和20元。</span>", "version": "1.0.2", "author": "quantcard", "stars": 1, "tags": ["选股", "择时", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "价格比较条件", "required": true, "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}], "default": "大于", "group": "price_filter"}, "price1": {"type": "number", "label": "价格条件1", "description": "价格条件1（元）", "required": true, "default": 10, "validation": {"min": 0, "max": 10000}, "unit": "元", "group": "price_filter"}, "price2": {"type": "number", "label": "价格条件2", "description": "价格条件2（元），仅在'区间'模式下使用", "required": false, "default": null, "validation": {"min": 0, "max": 10000}, "unit": "元", "group": "price_filter", "visibleWhen": {"parameter": "operator", "value": "区间"}}, "signal_type": {"type": "select", "label": "信号", "description": "择时策略产生的信号类型", "required": true, "options": [{"label": "买入", "value": "BUY"}, {"label": "卖出", "value": "SELL"}, {"label": "观望", "value": "HOLD"}], "default": "BUY", "group": "signal_settings", "visibleWhen": {"parameter": "@strategy_mode", "value": "timing"}}}, "parameterGroups": {"price_filter": {"parameters": ["operator", "price1", "price2"], "displayMode": "inline", "prefix": "价格", "separator": "-", "layout": "horizontal"}, "signal_settings": {"parameters": ["signal_type"], "displayMode": "inline", "prefix": "信号", "separator": "", "layout": "horizontal", "visibleWhen": {"parameter": "@strategy_mode", "value": "timing"}}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"代码": {"type": "string"}, "名称": {"type": "string"}, "最新价": {"type": "number"}}}}}, "ui": {"icon": "filter", "color": "#1890ff", "group": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}