{"template_id": "performance_forecast_filter", "name": "业绩预告", "description": "根据业绩预告信息筛选股票", "version": "1.0.0", "author": "QuantCard", "stars": 2, "tags": ["基本面", "选股", "财务"], "parameters": {"forecast_indicator": {"type": "select", "label": "预测指标", "description": "业绩预测指标类型", "required": true, "default": "全部", "options": [{"label": "全部", "value": "全部"}, {"label": "净利润", "value": "净利润"}, {"label": "营业收入", "value": "营业收入"}, {"label": "每股收益", "value": "每股收益"}]}, "change_magnitude_operator": {"type": "select", "label": "变动幅度条件", "description": "业绩变动幅度筛选条件", "required": true, "default": "不做限制", "options": [{"label": "不做限制", "value": "不做限制"}, {"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}]}, "change_magnitude_value": {"type": "number", "label": "变动幅度值", "description": "业绩变动幅度值", "required": true, "default": 0, "validation": {"min": -100, "max": 1000}, "unit": "%", "visibleWhen": {"parameter": "change_magnitude_operator", "value": ["大于", "小于"]}}, "forecast_type": {"type": "select", "label": "预告类型", "description": "业绩预告类型", "required": true, "default": "全部", "options": [{"label": "全部", "value": "全部"}, {"label": "略减", "value": "略减"}, {"label": "首亏", "value": "首亏"}, {"label": "预增", "value": "预增"}, {"label": "略增", "value": "略增"}, {"label": "续盈", "value": "续盈"}, {"label": "减亏", "value": "减亏"}, {"label": "扭亏", "value": "扭亏"}, {"label": "续亏", "value": "续亏"}]}}, "parameterGroups": {"indicator_filter": {"parameters": ["forecast_indicator"], "displayMode": "inline", "prefix": "预测指标", "separator": "-", "layout": "horizontal"}, "change_magnitude_filter": {"parameters": ["change_magnitude_operator", "change_magnitude_value"], "displayMode": "inline", "prefix": "业绩变动幅度", "separator": "-", "layout": "horizontal"}, "forecast_type_filter": {"parameters": ["forecast_type"], "displayMode": "inline", "prefix": "预告类型", "separator": "-", "layout": "horizontal"}}, "ui": {"icon": "LineChartOutlined", "color": "#52C41A", "group": "基本面", "order": 3, "form": {"layout": "vertical", "labelWidth": "100px", "wrapperWidth": "300px"}}, "template_code": "performance_forecast_filter/strategy.py"}