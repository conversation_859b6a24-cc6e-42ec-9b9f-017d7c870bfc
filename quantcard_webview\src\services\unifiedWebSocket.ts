/**
 * 🌐 统一WebSocket客户端 - 替换socket.io
 * 支持游戏交互、策略执行、库存管理和实时通信
 */

// 🌐 消息类型枚举
export enum MessageType {
  GAME_SESSION = 'game_session',
  STRATEGY_EXECUTION = 'strategy_execution',
  INVENTORY = 'inventory',
  MARKET_DATA = 'market_data',
  GAME_EVENT = 'game_event',
  AUTH = 'auth',
  HEARTBEAT = 'heartbeat',
  CONNECTION = 'connection',
  ERROR = 'error'
}

// 🌐 统一消息格式
export interface QuantCardMessage {
  type: MessageType
  subtype?: string
  request_id?: string
  user_id?: string
  session_id?: string
  strategy_id?: string
  group_id?: string
  template_id?: string
  payload: Record<string, any>
  timestamp: string
  sequence?: number
  priority?: 'low' | 'normal' | 'high'
}

// 🌐 连接配置
export interface WebSocketConfig {
  url?: string
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  reconnectInterval?: number
}

// 🌐 事件监听器类型
export type MessageHandler = (message: QuantCardMessage) => void
export type ConnectionHandler = (connected: boolean) => void
export type ErrorHandler = (error: Error) => void

// 🌐 请求回调
interface RequestCallback {
  resolve: (value: any) => void
  reject: (error: any) => void
  timeout: NodeJS.Timeout
}

class UnifiedWebSocketService {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private reconnectAttempts = 0
  private heartbeatTimer: NodeJS.Timeout | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  private messageSequence = 0
  
  // 🌐 事件监听器
  private messageHandlers = new Map<string, Set<MessageHandler>>()
  private connectionHandlers = new Set<ConnectionHandler>()
  private errorHandlers = new Set<ErrorHandler>()
  
  // 🌐 请求-响应管理
  private requestCallbacks = new Map<string, RequestCallback>()
  
  // 🌐 连接状态
  private _connected = false
  private _connecting = false

  constructor(config: WebSocketConfig = {}) {
    this.config = {
      autoReconnect: true,
      maxReconnectAttempts: 10,
      heartbeatInterval: 25000,
      reconnectInterval: 3000,
      ...config
    }
  }

  // 🌐 连接管理
  async connect(token?: string, isGuest = false, userId?: string): Promise<void> {
    if (this._connecting || this._connected) {
      return
    }

    this._connecting = true

    try {
      // 构建WebSocket URL
      let url = '/ws/unified'
      const params = new URLSearchParams()
      
      if (token) {
        params.append('token', token)
        if (userId) {
          params.append('user_id', userId)
        }
      }
      if (isGuest) {
        params.append('user_id', userId || `guest_${Date.now()}`)
      }
      
      if (params.toString()) {
        url += `?${params.toString()}`
      }

      // 对于WebSocket，需要完整的URL
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsHost = process.env.NODE_ENV === 'development' 
        ? `${window.location.hostname}:8000`
        : window.location.host
      
      const fullUrl = `${wsProtocol}//${wsHost}${url}`

      return new Promise((resolve, reject) => {
        try {
          this.ws = new WebSocket(fullUrl)
          
          // 设置连接超时
          const connectionTimeout = setTimeout(() => {
            this._connecting = false
            if (this.ws) {
              this.ws.close()
            }
            reject(new Error('连接超时'))
          }, 5000)
          
          this.ws.onopen = () => {
            clearTimeout(connectionTimeout)
            this._connected = true
            this._connecting = false
            this.reconnectAttempts = 0
            this.startHeartbeat()
            
            console.log('🌐 WebSocket连接已建立')
            
            // 通知连接成功
            this.connectionHandlers.forEach(handler => {
              try {
                handler(true)
              } catch (error) {
                console.error('连接处理器错误:', error)
              }
            })
            
            resolve()
          }
          
          this.ws.onmessage = this.handleMessage.bind(this)
          this.ws.onclose = this.handleClose.bind(this)
          this.ws.onerror = (error) => {
            clearTimeout(connectionTimeout)
            this._connecting = false
            
            // 只在非重连情况下记录错误
            if (this.reconnectAttempts === 0) {
              console.warn('🌐 WebSocket初次连接失败，将尝试重连')
            }
            
            reject(error)
          }
          
        } catch (error) {
          this._connecting = false
          reject(error)
        }
      })
      
    } catch (error) {
      this._connecting = false
      throw error
    }
  }

  disconnect(): void {
    this._connected = false
    this._connecting = false
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    // 清理所有待处理的请求
    this.requestCallbacks.forEach(callback => {
      clearTimeout(callback.timeout)
      callback.reject(new Error('连接已断开'))
    })
    this.requestCallbacks.clear()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    // 通知连接断开
    this.connectionHandlers.forEach(handler => {
      try {
        handler(false)
      } catch (error) {
        console.error('断开连接处理器错误:', error)
      }
    })
  }

  // 🌐 消息发送
  async sendMessage<T = any>(message: Partial<QuantCardMessage>): Promise<T> {
    if (!this._connected || !this.ws) {
      throw new Error('WebSocket未连接')
    }

    const requestId = message.request_id || this.generateRequestId()
    const fullMessage: QuantCardMessage = {
      type: MessageType.GAME_EVENT,
      payload: {},
      timestamp: new Date().toISOString(),
      sequence: ++this.messageSequence,
      priority: 'normal',
      ...message,
      request_id: requestId
    }

    return new Promise((resolve, reject) => {
      // 设置响应回调
      const timeout = setTimeout(() => {
        this.requestCallbacks.delete(requestId)
        reject(new Error('请求超时'))
      }, 10000)

      this.requestCallbacks.set(requestId, { resolve, reject, timeout })

      try {
        this.ws!.send(JSON.stringify(fullMessage))
      } catch (error) {
        this.requestCallbacks.delete(requestId)
        clearTimeout(timeout)
        reject(error)
      }
    })
  }

  // 🌐 发送无响应消息
  sendEvent(message: Partial<QuantCardMessage>): void {
    if (!this._connected || !this.ws) {
      console.warn('WebSocket未连接，消息未发送')
      return
    }

    const fullMessage: QuantCardMessage = {
      type: MessageType.GAME_EVENT,
      payload: {},
      timestamp: new Date().toISOString(),
      sequence: ++this.messageSequence,
      priority: 'normal',
      ...message
    }

    try {
      this.ws.send(JSON.stringify(fullMessage))
    } catch (error) {
      console.error('发送事件失败:', error)
      this.handleError(error instanceof Error ? error : new Error('发送失败'))
    }
  }

  // 🌐 游戏会话管理
  async joinGameSession(sessionType: 'battle' | 'adventure' | 'strategy_creation', strategyGroupId?: string) {
    return this.sendMessage({
      type: MessageType.GAME_SESSION,
      subtype: 'join',
      payload: {
        session_type: sessionType,
        strategy_group_id: strategyGroupId
      }
    })
  }

  async changeScene(scene: string, sceneData?: any) {
    return this.sendMessage({
      type: MessageType.GAME_SESSION,
      subtype: 'scene_change',
      payload: { scene, scene_data: sceneData }
    })
  }

  async leaveGameSession(sessionId?: string) {
    return this.sendMessage({
      type: MessageType.GAME_SESSION,
      subtype: 'leave',
      session_id: sessionId
    })
  }

  // 🌐 策略执行
  async executeStrategyInBattle(strategyConfig: any) {
    return this.sendMessage({
      type: MessageType.STRATEGY_EXECUTION,
      subtype: 'start',
      payload: {
        strategy_config: strategyConfig,
        real_time: true
      }
    })
  }

  // 🌐 库存操作
  async consumeCard(templateId: string, quantity: number = 1, reason: string = 'game_action') {
    return this.sendMessage({
      type: MessageType.INVENTORY,
      subtype: 'consume',
      template_id: templateId,
      payload: { template_id: templateId, quantity, reason }
    })
  }

  async addCard(templateId: string, quantity: number = 1, source: string = 'game_reward') {
    return this.sendMessage({
      type: MessageType.INVENTORY,
      subtype: 'add',
      template_id: templateId,
      payload: { template_id: templateId, quantity, source }
    })
  }

  async getInventory(availableOnly: boolean = false) {
    return this.sendMessage({
      type: MessageType.INVENTORY,
      subtype: 'list',
      payload: { available_only: availableOnly }
    })
  }

  // 🌐 认证相关
  async guestLogin() {
    return this.sendMessage({
      type: MessageType.AUTH,
      subtype: 'guest_login'
    })
  }

  // 🌐 事件监听管理
  subscribe(messageType: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set())
    }
    
    this.messageHandlers.get(messageType)!.add(handler)
    
    return () => {
      const handlers = this.messageHandlers.get(messageType)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          this.messageHandlers.delete(messageType)
        }
      }
    }
  }

  onConnection(handler: ConnectionHandler): () => void {
    this.connectionHandlers.add(handler)
    return () => this.connectionHandlers.delete(handler)
  }

  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler)
    return () => this.errorHandlers.delete(handler)
  }

  // 🌐 状态查询
  get connected(): boolean {
    return this._connected
  }

  get connecting(): boolean {
    return this._connecting
  }

  // 🌐 私有方法
  private handleMessage(event: MessageEvent) {
    try {
      const message: QuantCardMessage = JSON.parse(event.data)
      
      // 处理请求-响应
      if (message.request_id && this.requestCallbacks.has(message.request_id)) {
        const callback = this.requestCallbacks.get(message.request_id)!
        clearTimeout(callback.timeout)
        this.requestCallbacks.delete(message.request_id)
        
        if (message.type === MessageType.ERROR) {
          callback.reject(new Error(message.payload.error || '请求失败'))
        } else {
          callback.resolve(message.payload)
        }
        return
      }
      
      // 分发消息给订阅者
      const handlers = this.messageHandlers.get(message.type)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            console.error('消息处理器错误:', error)
          }
        })
      }
      
      // 处理通用消息
      this.handleCommonMessages(message)
      
    } catch (error) {
      console.error('WebSocket消息解析错误:', error)
      this.handleError(error instanceof Error ? error : new Error('消息解析失败'))
    }
  }

  private handleCommonMessages(message: QuantCardMessage) {
    switch (message.type) {
      case MessageType.CONNECTION:
        if (message.subtype === 'established') {
          console.log('🌐 WebSocket连接已建立:', message.payload)
        }
        break
        
      case MessageType.AUTH:
        if (message.subtype === 'guest_mode') {
          console.log('👤 访客模式已启用:', message.payload)
        }
        break
        
      case MessageType.ERROR:
        this.handleError(new Error(message.payload.error || '服务器错误'))
        break
        
      case MessageType.HEARTBEAT:
        // 心跳响应，无需处理
        break
    }
  }

  private handleClose(event: CloseEvent) {
    this._connected = false
    this._connecting = false
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    console.log('🌐 WebSocket连接已关闭:', event.code, event.reason)
    
    // 通知连接断开
    this.connectionHandlers.forEach(handler => {
      try {
        handler(false)
      } catch (error) {
        console.error('断开连接处理器错误:', error)
      }
    })
    
    // 自动重连
    if (this.config.autoReconnect && this.reconnectAttempts < this.config.maxReconnectAttempts!) {
      this.scheduleReconnect()
    }
  }

  private handleError(error: Error) {
    console.error('🌐 WebSocket错误:', error)
    this.errorHandlers.forEach(handler => {
      try {
        handler(error)
      } catch (handlerError) {
        console.error('错误处理器错误:', handlerError)
      }
    })
  }

  private scheduleReconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }
    
    this.reconnectAttempts++
    const delay = Math.min(this.config.reconnectInterval! * this.reconnectAttempts, 30000)
    
    console.log(`🔄 ${delay / 1000}秒后尝试重连 (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)
    
    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        console.error('重连失败:', error)
        if (this.reconnectAttempts < this.config.maxReconnectAttempts!) {
          this.scheduleReconnect()
        } else {
          console.error('WebSocket重连失败次数过多，停止重连')
        }
      }
    }, delay)
  }

  private startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }
    
    this.heartbeatTimer = setInterval(() => {
      if (this._connected && this.ws) {
        this.sendEvent({
          type: MessageType.HEARTBEAT,
          subtype: 'ping'
        })
      }
    }, this.config.heartbeatInterval!)
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 🌐 导出单例
export const unifiedWebSocket = new UnifiedWebSocketService()
export default UnifiedWebSocketService 