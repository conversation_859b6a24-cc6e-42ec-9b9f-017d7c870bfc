import type { StrategyTemplate } from '../../types/game'

export const strategyTemplates: StrategyTemplate[] = [
  {
    id: 'rsi_strategy',
    name: 'RSI超买超卖策略',
    description: '基于RSI指标识别超买超卖机会，适合震荡市场',
    category: 'timing',
    rarity: 'common',
    parameters: { period: 14, oversold: 30, overbought: 70 },
    icon: '📊',
    tags: ['技术指标', 'RSI', '震荡']
  },
  {
    id: 'macd_strategy',
    name: 'MACD金叉死叉策略',
    description: '利用MACD指标的金叉死叉信号进行趋势跟踪',
    category: 'timing',
    rarity: 'rare',
    parameters: { fast: 12, slow: 26, signal: 9 },
    icon: '📈',
    tags: ['趋势跟踪', 'MACD', '金叉']
  },
  {
    id: 'bollinger_bands',
    name: '布林带突破策略',
    description: '基于布林带的价格突破和回归策略',
    category: 'timing',
    rarity: 'epic',
    parameters: { period: 20, std: 2 },
    icon: '🎯',
    tags: ['布林带', '突破', '回归']
  },
  {
    id: 'moving_average_cross',
    name: '双均线交叉策略',
    description: '使用快慢两条移动平均线的交叉信号',
    category: 'timing',
    rarity: 'common',
    parameters: { fast_period: 5, slow_period: 20 },
    icon: '〰️',
    tags: ['均线', '交叉', '趋势']
  },
  {
    id: 'stop_loss_strategy',
    name: '智能止损策略',
    description: '动态调整止损位，保护利润并控制风险',
    category: 'risk_management',
    rarity: 'legendary',
    parameters: { stop_loss_pct: 5, trailing_stop: true },
    icon: '🛡️',
    tags: ['止损', '风控', '保护']
  },
  {
    id: 'volume_filter',
    name: '成交量过滤器',
    description: '基于成交量筛选活跃度高的股票',
    category: 'filter',
    rarity: 'rare',
    parameters: { min_volume: 1000000, volume_ratio: 1.5 },
    icon: '📈',
    tags: ['成交量', '筛选', '活跃']
  },
  {
    id: 'momentum_strategy',
    name: '动量策略',
    description: '捕捉股价动量变化，追涨杀跌',
    category: 'timing',
    rarity: 'epic',
    parameters: { lookback: 20, momentum_threshold: 0.05 },
    icon: '🚀',
    tags: ['动量', '追涨', '趋势']
  },
  {
    id: 'mean_reversion',
    name: '均值回归策略',
    description: '基于价格偏离均值的回归特性进行交易',
    category: 'timing',
    rarity: 'legendary',
    parameters: { lookback: 30, deviation_threshold: 2 },
    icon: '🔄',
    tags: ['均值回归', '偏离', '修正']
  }
] 
