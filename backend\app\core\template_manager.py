"""
策略模板管理器
"""
from pathlib import Path
import json
import logging
from typing import Dict, Any, Optional, List
from ..models.strategy_template import StrategyTemplate

logger = logging.getLogger(__name__)

class TemplateManager:
    """策略模板管理器"""
    
    def __init__(self):
        self.templates_dir = Path(__file__).parent.parent / 'strategies' / 'templates'
        self.template_cache = {}
    
    def get_template_file_path(self, template_code: str) -> Optional[Path]:
        """获取模板文件路径"""
        try:
            template_code = template_code.replace('\\', '/')
            if 'templates/' in template_code:
                template_type = template_code.split('templates/')[1].split('/')[0]
                return self.templates_dir / template_type / 'template.json'
        except Exception as e:
            logger.error(f"Error getting template file path: {str(e)}")
        return None
    
    def load_template_file(self, template_path: Path) -> Optional[Dict[str, Any]]:
        """加载模板文件"""
        try:
            if template_path.exists():
                # 检查缓存
                if str(template_path) in self.template_cache:
                    return self.template_cache[str(template_path)]
                
                # 读取文件
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_data = json.load(f)
                    # 更新缓存
                    self.template_cache[str(template_path)] = template_data
                    return template_data
        except Exception as e:
            logger.error(f"Error loading template file {template_path}: {str(e)}")
        return None
    
    def merge_template_data(self, db_template: StrategyTemplate, file_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并数据库模板和文件模板数据"""
        # 基础模板数据
        merged_data = {
            "id": str(db_template.id),
            "name": db_template.name,
            "description": db_template.description,
            "category": db_template.category,
            "version": db_template.version,
            "author": db_template.author,
            "stars": db_template.stars,
            "tags": db_template.tags,
            "parameters": db_template.parameters,
            "parameterGroups": db_template.parameterGroups or {},
            "ui": db_template.ui or {
                "form": {
                    "layout": "horizontal",
                    "compact": True,
                    "showDescription": False
                }
            },
            "is_active": db_template.is_active,
            "template_code": db_template.template_code,
            "created_at": db_template.created_at,
            "updated_at": db_template.updated_at
        }
        
        # 如果有文件数据，覆盖相应字段
        if file_data:
            merged_data.update({
                "parameters": file_data.get("parameters", merged_data["parameters"]),
                "parameterGroups": file_data.get("parameterGroups", merged_data["parameterGroups"]),
                "ui": file_data.get("ui", merged_data["ui"]),
                "stars": file_data.get("stars", merged_data["stars"])
            })
        
        return merged_data
    
    async def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取单个模板详情"""
        try:
            # 从数据库获取模板
            template = await StrategyTemplate.get(template_id)
            if not template:
                return None
            
            # 获取模板文件数据
            template_data = None
            if template.template_code:
                template_path = self.get_template_file_path(template.template_code)
                if template_path:
                    template_data = self.load_template_file(template_path)
            
            # 合并数据
            return self.merge_template_data(template, template_data)
            
        except Exception as e:
            logger.error(f"Error getting template {template_id}: {str(e)}")
            return None
    
    async def list_templates(
        self,
        skip: int = 0,
        limit: int = 10,
        category: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取模板列表"""
        try:
            # 构建查询条件
            query = {}
            if category:
                query["category"] = category
            if keyword:
                query["$or"] = [
                    {"name": {"$regex": keyword, "$options": "i"}},
                    {"description": {"$regex": keyword, "$options": "i"}}
                ]
            
            # 查询数据库
            templates = await StrategyTemplate.find(query).skip(skip).limit(limit).to_list()
            total = await StrategyTemplate.find(query).count()
            
            # 处理每个模板
            formatted_templates = []
            for template in templates:
                # 获取模板文件数据
                template_data = None
                if template.template_code:
                    template_path = self.get_template_file_path(template.template_code)
                    if template_path:
                        template_data = self.load_template_file(template_path)
                
                # 合并数据
                formatted_template = self.merge_template_data(template, template_data)
                formatted_templates.append(formatted_template)
            
            return formatted_templates, total
            
        except Exception as e:
            logger.error(f"Error listing templates: {str(e)}")
            raise

# 创建全局实例
template_manager = TemplateManager() 
 