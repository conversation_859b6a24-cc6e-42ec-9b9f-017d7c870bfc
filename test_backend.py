#!/usr/bin/env python3
"""
后端服务测试脚本
验证WebSocket连接和API端点是否正常工作
"""

import asyncio
import aiohttp
import json
import websockets
from datetime import datetime

async def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # 测试根路径
        try:
            async with session.get(f"{base_url}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 根路径测试成功: {data}")
                else:
                    print(f"❌ 根路径测试失败: {response.status}")
        except Exception as e:
            print(f"❌ 根路径连接失败: {e}")
            return False
        
        # 测试卡包API
        try:
            async with session.get(f"{base_url}/api/cards/packs") as response:
                print(f"📦 卡包API状态: {response.status}")
                if response.status == 401:
                    print("✅ 卡包API需要认证（正常）")
                elif response.status == 200:
                    data = await response.json()
                    print(f"✅ 卡包API响应: {data}")
                else:
                    print(f"⚠️ 卡包API异常状态: {response.status}")
        except Exception as e:
            print(f"❌ 卡包API测试失败: {e}")
    
    return True

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("🌐 测试WebSocket连接...")
    
    ws_url = "ws://localhost:8000/ws/unified?user_id=test_user"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket连接建立成功")
            
            # 等待连接确认消息
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                print(f"📨 收到消息: {data}")
                
                if data.get("type") == "connection" and data.get("subtype") == "established":
                    print("✅ WebSocket连接确认成功")
                    return True
                else:
                    print(f"⚠️ 收到意外消息: {data}")
                    
            except asyncio.TimeoutError:
                print("❌ 等待连接确认消息超时")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始后端服务测试...")
    print(f"⏰ 测试时间: {datetime.now().isoformat()}")
    print("-" * 50)
    
    # 测试API端点
    api_success = await test_api_endpoints()
    print("-" * 50)
    
    # 测试WebSocket连接
    ws_success = await test_websocket_connection()
    print("-" * 50)
    
    # 总结
    if api_success and ws_success:
        print("🎉 所有测试通过！后端服务运行正常")
        return True
    else:
        print("❌ 部分测试失败，请检查后端服务")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"💥 测试过程中发生错误: {e}")
        exit(1)
