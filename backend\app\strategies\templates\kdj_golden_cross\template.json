{"id": "kdj_golden_cross", "name": "KDJ黄金交叉策略", "description": "基于KDJ随机指标的黄金交叉择时策略，在超买超卖区域捕捉反转机会", "version": "1.0.0", "author": "QuantCard团队", "tags": ["择时", "KDJ交叉", "技术指标", "随机指标", "黄金交叉"], "createTime": "2024-01-01T00:00:00Z", "updateTime": "2024-01-01T00:00:00Z", "parameters": {"k_period": {"label": "K值计算周期", "type": "number", "default": 9, "min": 5, "max": 21, "required": true, "placeholder": "请输入K值周期", "description": "KDJ指标中K值的计算周期，标准为9", "unit": "分钟"}, "oversold_level": {"label": "KDJ超卖线", "type": "number", "default": 20.0, "min": 10.0, "max": 30.0, "required": true, "placeholder": "请输入超卖阈值", "description": "KDJ低于此值认为超卖，标准为20", "unit": ""}, "overbought_level": {"label": "KDJ超买线", "type": "number", "default": 80.0, "min": 70.0, "max": 90.0, "required": true, "placeholder": "请输入超买阈值", "description": "KDJ高于此值认为超买，标准为80", "unit": ""}, "use_j_confirmation": {"label": "启用J值确认", "type": "switch", "default": true, "required": true, "description": "使用J值方向确认交叉信号的有效性"}}, "outputs": [{"name": "signals", "type": "signal", "description": "KDJ交叉交易信号", "fields": ["symbol", "name", "direction", "trigger_condition", "k_value", "d_value", "j_value", "cross_strength"]}], "modes": [{"name": "timing", "label": "择时模式", "description": "实时监控KDJ交叉信号"}, {"name": "backtest", "label": "回测模式", "description": "使用历史数据验证KDJ策略"}], "supportedDataSources": ["questdb"], "examples": [{"name": "标准KDJ交叉", "description": "经典的KDJ黄金交叉参数", "parameters": {"k_period": 9, "oversold_level": 20.0, "overbought_level": 80.0, "use_j_confirmation": true}}], "template_id": "timing", "ui": {"icon": "stockOutlined", "color": "#52c41a", "group": "择时", "order": 3}}