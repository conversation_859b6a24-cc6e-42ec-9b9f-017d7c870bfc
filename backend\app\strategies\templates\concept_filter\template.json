{"template_id": "concept_filter", "name": "概念名称", "description": "根据概念名称中包含的关键字筛选股票   <span style='color: #808080'>例：筛选概念中包含'元宇宙'的股票。</span>", "version": "1.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "概念"], "parameters": {"keywords": {"type": "string", "label": "关键字", "description": "多个关键字用逗号分隔，满足任一关键字即可", "required": true, "default": "", "group": "concept_filter"}}, "parameterGroups": {"concept_filter": {"parameters": ["keywords"], "displayMode": "inline", "prefix": "概念名称包含", "separator": "", "layout": "horizontal"}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"股票代码": {"type": "string"}, "股票名称": {"type": "string"}, "概念名称": {"type": "string"}}}}}, "ui": {"icon": "tag", "color": "#1890ff", "group": "筛选", "order": 3, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}