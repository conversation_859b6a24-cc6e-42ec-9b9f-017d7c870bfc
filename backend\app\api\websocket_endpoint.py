"""
统一WebSocket端点
提供游戏和策略的WebSocket连接入口
"""
import logging
from typing import Optional
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException

from .unified_websocket import UnifiedWebSocket<PERSON>and<PERSON>, websocket_manager
from ..core.deps import get_current_user_ws
from ..models.user import User

logger = logging.getLogger(__name__)

# 创建WebSocket路由器
ws_router = APIRouter()

@ws_router.websocket("/ws/unified")
async def unified_websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="认证令牌"),
    user_id: Optional[str] = Query(None, description="用户ID（访客模式）"),
    session_type: str = Query("game", description="会话类型")
):
    """
    统一WebSocket端点 - 支持游戏和策略
    支持认证用户和访客模式
    """
    handler = UnifiedWebSocketHandler()
    authenticated_user_id = None
    is_guest = False
    
    try:
        # 尝试认证用户
        if token:
            try:
                # TODO: 实现真正的WebSocket token验证
                authenticated_user_id = user_id or "authenticated_user"
                logger.info(f"认证用户 {authenticated_user_id} 连接WebSocket")
                
            except Exception as e:
                logger.warning(f"WebSocket认证失败: {e}")
                await websocket.close(code=4001, reason="认证失败")
                return
        else:
            # 访客模式
            is_guest = True
            authenticated_user_id = user_id or f"guest_{websocket.headers.get('sec-websocket-key', 'unknown')[:8]}"
            logger.info(f"访客用户 {authenticated_user_id} 连接WebSocket")
        
        # 建立连接
        await websocket_manager.connect_user(websocket, authenticated_user_id)
        
        # 发送连接成功消息
        connection_message = {
            "type": "connection",
            "subtype": "established",
            "payload": {
                "user_id": authenticated_user_id,
                "is_guest": is_guest,
                "session_type": session_type,
                "server_time": datetime.utcnow().isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        await handler.send_response(websocket, connection_message)
        
        # 如果是访客，发送访客权限信息
        if is_guest:
            guest_info = {
                "type": "auth",
                "subtype": "guest_mode",
                "payload": {
                    "guest_id": authenticated_user_id,
                    "permissions": ["browse", "view_worldmap"],
                    "limitations": ["no_inventory", "no_strategy_execution"]
                }
            }
            await handler.send_response(websocket, guest_info)
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                
                # 路由处理消息
                await handler.route_message(websocket, data, authenticated_user_id)
                
            except WebSocketDisconnect:
                logger.info(f"用户 {authenticated_user_id} 主动断开连接")
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息异常: {e}")
                await handler.send_error(websocket, f"处理消息失败: {str(e)}")
                
    except WebSocketDisconnect:
        logger.info(f"用户 {authenticated_user_id} 连接断开")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {e}")
    finally:
        # 清理连接
        await websocket_manager.disconnect_user(websocket)

@ws_router.websocket("/ws/strategy")
async def strategy_websocket_endpoint(
    websocket: WebSocket,
    user_id: str = Depends(get_current_user_ws)
):
    """
    策略专用WebSocket端点（向后兼容）
    """
    handler = UnifiedWebSocketHandler()
    
    try:
        # 建立连接
        await websocket_manager.connect_user(websocket, user_id)
        
        # 发送连接成功消息
        connection_message = {
            "type": "connection",
            "subtype": "established",
            "payload": {
                "user_id": user_id,
                "is_guest": False,
                "session_type": "strategy",
                "server_time": datetime.utcnow().isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        await handler.send_response(websocket, connection_message)
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                
                # 路由处理消息
                await handler.route_message(websocket, data, user_id)
                
            except WebSocketDisconnect:
                logger.info(f"用户 {user_id} 主动断开策略WebSocket连接")
                break
            except Exception as e:
                logger.error(f"处理策略WebSocket消息异常: {e}")
                await handler.send_error(websocket, f"处理消息失败: {str(e)}")
                
    except WebSocketDisconnect:
        logger.info(f"用户 {user_id} 策略WebSocket连接断开")
    except Exception as e:
        logger.error(f"策略WebSocket连接异常: {e}")
    finally:
        # 清理连接
        await websocket_manager.disconnect_user(websocket) 