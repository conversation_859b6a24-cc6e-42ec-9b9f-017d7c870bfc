{"template_id": "ATR_filter", "name": "振幅", "description": "根据设定的振幅条件筛选股票，支持区间范围筛选   <span style='color: #808080'>例：振幅小于1。</span>", "version": "1.0.2", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "振幅比较条件", "required": true, "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "区间", "value": "区间"}], "default": "大于", "group": "ATR_filter"}, "ATR1": {"type": "number", "label": "振幅条件1", "description": "振幅条件1（%）", "required": true, "default": 10, "validation": {"min": 0, "max": 10000}, "unit": "%", "group": "ATR_filter"}, "ATR2": {"type": "number", "label": "振幅条件2", "description": "振幅条件2（%），仅在'区间'模式下使用", "required": false, "default": null, "validation": {"min": 0, "max": 10000}, "unit": "%", "group": "ATR_filter", "visibleWhen": {"parameter": "operator", "value": "区间"}}}, "parameterGroups": {"ATR_filter": {"parameters": ["operator", "ATR1", "ATR2"], "displayMode": "inline", "prefix": "振幅", "separator": "-", "layout": "horizontal"}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"代码": {"type": "string"}, "名称": {"type": "string"}, "振幅": {"type": "number"}}}}}, "ui": {"icon": "filter", "color": "#1890ff", "group": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}