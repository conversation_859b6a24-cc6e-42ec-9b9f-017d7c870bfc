import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import styled from 'styled-components'
import type { StrategyCardInstance } from '../../types/game'

export type ParameterConfigItem = {
	key: string;
	label: string;
	type: 'number' | 'text' | 'select' | 'boolean';
	defaultValue: any;
	options?: string[];
	min?: number;
	max?: number;
}

const ParameterModal = styled(motion.div)`
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(8px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 2rem;
`

const ParameterContent = styled(motion.div)`
	background: rgba(255, 255, 255, 0.98);
	backdrop-filter: blur(20px);
	border-radius: 20px;
	padding: 2rem;
	max-width: 500px;
	width: 100%;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
`

const ParameterHeader = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1.5rem;
	padding-bottom: 1rem;
	border-bottom: 1px solid rgba(59, 130, 246, 0.2);
`

const ParameterTitle = styled.h3`
	color: #1e293b;
	font-size: 1.25rem;
	font-weight: 700;
	margin: 0;
`

const CloseButton = styled(motion.button)`
	background: rgba(100, 116, 139, 0.1);
	border: none;
	color: #64748b;
	width: 36px;
	height: 36px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 1.2rem;
	font-weight: bold;
	
	&:hover {
		background: rgba(100, 116, 139, 0.2);
		color: #1e293b;
	}
`

const ParameterForm = styled.div`
	display: flex;
	flex-direction: column;
	gap: 1rem;
`

const ParameterField = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
`

const ParameterLabel = styled.label`
	color: #374151;
	font-weight: 600;
	font-size: 0.9rem;
`

const ParameterInput = styled.input`
	padding: 0.75rem;
	border: 1px solid rgba(59, 130, 246, 0.3);
	border-radius: 8px;
	font-size: 0.9rem;
	background: white;
	
	&:focus {
		outline: none;
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
	}
`

const ParameterSelect = styled.select`
	padding: 0.75rem;
	border: 1px solid rgba(59, 130, 246, 0.3);
	border-radius: 8px;
	font-size: 0.9rem;
	background: white;
	
	&:focus {
		outline: none;
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
	}
`

const ParameterCheckbox = styled.div`
	display: flex;
	align-items: center;
	gap: 0.5rem;
	
	input[type="checkbox"] {
		width: 18px;
		height: 18px;
		accent-color: #3b82f6;
	}
`

const ParameterActions = styled.div`
	display: flex;
	gap: 1rem;
	justify-content: flex-end;
	margin-top: 1.5rem;
	padding-top: 1rem;
	border-top: 1px solid rgba(59, 130, 246, 0.1);
`

const ActionButton = styled(motion.button)<{ $variant?: 'primary' | 'secondary' | 'success' | 'danger' }>`
	padding: 0.625rem 1.25rem;
	border: 2px solid ${props => {
		switch(props.$variant) {
			case 'primary': return '#3b82f6'
			case 'success': return '#10b981'
			case 'danger': return '#ef4444'
			default: return '#64748b'
		}
	}};
	background: ${props => {
		switch(props.$variant) {
			case 'primary': return '#3b82f6'
			case 'success': return '#10b981'
			case 'danger': return '#ef4444'
			default: return 'transparent'
		}
	}};
	color: ${props => {
		switch(props.$variant) {
			case 'primary':
			case 'success':
			case 'danger': return 'white'
			default: return '#64748b'
		}
	}};
	border-radius: 10px;
	font-family: "Arial", sans-serif;
	font-size: 0.875rem;
	font-weight: 600;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: all 0.2s ease;
	
	&:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 12px ${props => {
			switch(props.$variant) {
				case 'primary': return 'rgba(59, 130, 246, 0.3)'
				case 'success': return 'rgba(16, 185, 129, 0.3)'
				case 'danger': return 'rgba(239, 68, 68, 0.3)'
				default: return 'rgba(100, 116, 139, 0.2)'
			}
		}};
	}
	
	&:active {
		transform: translateY(0);
	}
	
	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
	}
`

const DynamicParameterForm: React.FC<{
	card: StrategyCardInstance;
	parameterConfig: ParameterConfigItem[];
	onSave: (parameters: Record<string, any>) => void;
	onCancel: () => void;
}> = ({ card, parameterConfig, onSave, onCancel }) => {
	const [parameters, setParameters] = useState<Record<string, any>>(() => {
		const initialParams: Record<string, any> = {}
		parameterConfig.forEach(config => {
			initialParams[config.key] = card.parameters[config.key] ?? config.defaultValue
		})
		return initialParams
	})
	
	const handleParameterChange = (key: string, value: any) => {
		setParameters(prev => ({
			...prev,
			[key]: value
		}))
	}
	
	const handleSubmit = () => {
		onSave(parameters)
	}
	
	return (
		<>
			{parameterConfig.map(config => (
				<ParameterField key={config.key}>
					<ParameterLabel>{config.label}</ParameterLabel>
					{config.type === 'number' && (
						<ParameterInput
							type="number"
							value={parameters[config.key] ?? ''}
							min={config.min}
							max={config.max}
							onChange={(e) => handleParameterChange(config.key, parseFloat(e.target.value) || config.defaultValue)}
							placeholder={`输入${config.label}`}
						/>
					)}
					{config.type === 'text' && (
						<ParameterInput
							type="text"
							value={parameters[config.key] ?? ''}
							onChange={(e) => handleParameterChange(config.key, e.target.value)}
							placeholder={`输入${config.label}`}
						/>
					)}
					{config.type === 'select' && (
						<ParameterSelect
							value={parameters[config.key] ?? config.defaultValue}
							onChange={(e) => handleParameterChange(config.key, e.target.value)}
						>
							{config.options?.map(option => (
								<option key={option} value={option}>
									{option}
								</option>
							))}
						</ParameterSelect>
					)}
					{config.type === 'boolean' && (
						<ParameterCheckbox>
							<input
								type="checkbox"
								checked={parameters[config.key] ?? config.defaultValue}
								onChange={(e) => handleParameterChange(config.key, e.target.checked)}
							/>
							<span>启用此选项</span>
						</ParameterCheckbox>
					)}
				</ParameterField>
			))}
			
			<ParameterActions>
				<ActionButton
					onClick={onCancel}
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
				>
					取消
				</ActionButton>
				<ActionButton
					$variant="primary"
					onClick={handleSubmit}
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
				>
					保存
				</ActionButton>
			</ParameterActions>
		</>
	)
}

interface ParameterEditorProps {
	card: StrategyCardInstance
	parameterConfig: ParameterConfigItem[]
	onSave: (parameters: Record<string, any>) => void
	onCancel: () => void
}

const ParameterEditor: React.FC<ParameterEditorProps> = ({ card, parameterConfig, onSave, onCancel }) => {
	return (
		<AnimatePresence>
			{card && (
				<ParameterModal
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					onClick={onCancel}
				>
					<ParameterContent
						initial={{ scale: 0.8, opacity: 0 }}
						animate={{ scale: 1, opacity: 1 }}
						exit={{ scale: 0.8, opacity: 0 }}
						onClick={(e) => e.stopPropagation()}
					>
						<ParameterHeader>
							<ParameterTitle>
								⚙️ 编辑卡片参数 - {card.template_id.replace(/_/g, ' ')}
							</ParameterTitle>
							<CloseButton
								onClick={onCancel}
								whileHover={{ scale: 1.1 }}
								whileTap={{ scale: 0.9 }}
							>
								�?
							</CloseButton>
						</ParameterHeader>
						<ParameterForm>
							<DynamicParameterForm
								card={card}
								parameterConfig={parameterConfig}
								onSave={onSave}
								onCancel={onCancel}
							/>
						</ParameterForm>
					</ParameterContent>
				</ParameterModal>
			)}
		</AnimatePresence>
	)
}

export default ParameterEditor 
