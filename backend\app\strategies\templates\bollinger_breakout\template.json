{"id": "bollinger_breakout", "name": "布林带突破策略", "description": "基于布林带的智能突破策略，具备挤压形态识别、假突破过滤和动态止损功能", "version": "1.0.0", "author": "QuantCard团队", "tags": ["技术分析", "布林带", "突破策略", "挤压形态", "波动率分析", "均值回归"], "createTime": "2024-01-01T00:00:00Z", "updateTime": "2024-01-01T00:00:00Z", "parameters": {"bb_period": {"label": "布林带周期", "type": "number", "default": 20, "min": 10, "max": 50, "step": 1, "required": true, "placeholder": "请输入布林带计算周期", "description": "计算布林带的移动平均周期，建议15-25", "unit": "分钟", "category": "基础设置"}, "bb_std": {"label": "标准差倍数", "type": "number", "default": 2.0, "min": 1.0, "max": 3.0, "step": 0.1, "required": true, "placeholder": "请输入标准差倍数", "description": "布林带上下轨的标准差倍数，2.0为经典设置", "unit": "倍", "category": "基础设置"}, "adaptive_std": {"label": "自适应标准差", "type": "switch", "default": false, "required": true, "description": "根据市场波动率动态调整标准差倍数", "category": "高级设置"}, "squeeze_threshold": {"label": "挤压识别阈值", "type": "number", "default": 0.1, "min": 0.05, "max": 0.5, "step": 0.05, "required": true, "placeholder": "请输入挤压识别阈值", "description": "布林带收窄程度阈值，越小越严格", "unit": "", "category": "挤压设置"}, "min_squeeze_periods": {"label": "最小挤压周期", "type": "number", "default": 5, "min": 3, "max": 15, "step": 1, "required": true, "placeholder": "请输入最小挤压周期", "description": "挤压状态持续的最小周期数", "unit": "分钟", "category": "挤压设置"}, "min_volume_ratio": {"label": "最小成交量倍数", "type": "number", "default": 1.5, "min": 1.0, "max": 3.0, "step": 0.1, "required": true, "placeholder": "请输入最小成交量倍数", "description": "突破时成交量须超过均量的倍数", "unit": "倍", "category": "突破确认"}, "breakout_confirmation": {"label": "突破确认周期", "type": "number", "default": 2, "min": 1, "max": 5, "step": 1, "required": true, "placeholder": "请输入确认周期", "description": "突破信号的确认等待周期", "unit": "分钟", "category": "突破确认"}, "use_squeeze_filter": {"label": "挤压后突破优先", "type": "switch", "default": true, "required": true, "description": "优先考虑挤压形态后的突破信号", "category": "信号过滤"}, "rsi_oversold": {"label": "RSI超卖线", "type": "number", "default": 30.0, "min": 20.0, "max": 40.0, "step": 1.0, "required": true, "placeholder": "请输入RSI超卖阈值", "description": "RSI超卖水平，用于背离分析", "unit": "", "category": "辅助指标"}, "rsi_overbought": {"label": "RSI超买线", "type": "number", "default": 70.0, "min": 60.0, "max": 80.0, "step": 1.0, "required": true, "placeholder": "请输入RSI超买阈值", "description": "RSI超买水平，用于背离分析", "unit": "", "category": "辅助指标"}}, "outputs": [{"name": "breakout_signals", "type": "signal", "description": "布林带突破交易信号", "fields": ["symbol", "name", "direction", "confidence", "trigger_condition", "current_price", "bb_upper", "bb_mid", "bb_lower", "bb_position", "bb_width", "breakout_type", "stop_loss", "volume_ratio", "rsi", "is_squeeze", "post_squeeze_breakout"]}], "modes": [{"name": "timing", "label": "择时模式", "description": "实时监控布林带突破机会"}, {"name": "backtest", "label": "回测模式", "description": "历史数据验证策略效果"}, {"name": "monitor", "label": "监控模式", "description": "监控挤压形态和突破预警"}], "supportedDataSources": ["questdb"], "examples": [{"name": "经典布林带突破", "description": "标准的2倍标准差布林带突破策略", "parameters": {"bb_period": 20, "bb_std": 2.0, "adaptive_std": false, "squeeze_threshold": 0.1, "min_squeeze_periods": 5, "min_volume_ratio": 1.5, "breakout_confirmation": 2, "use_squeeze_filter": true, "rsi_oversold": 30.0, "rsi_overbought": 70.0}}, {"name": "敏感型突破策略", "description": "更敏感的参数设置，适合活跃市场", "parameters": {"bb_period": 15, "bb_std": 1.8, "adaptive_std": true, "squeeze_threshold": 0.15, "min_squeeze_periods": 3, "min_volume_ratio": 1.3, "breakout_confirmation": 1, "use_squeeze_filter": true, "rsi_oversold": 25.0, "rsi_overbought": 75.0}}, {"name": "保守型突破策略", "description": "更保守的参数，减少假突破", "parameters": {"bb_period": 25, "bb_std": 2.2, "adaptive_std": false, "squeeze_threshold": 0.08, "min_squeeze_periods": 8, "min_volume_ratio": 2.0, "breakout_confirmation": 3, "use_squeeze_filter": true, "rsi_oversold": 35.0, "rsi_overbought": 65.0}}], "template_id": "timing", "ui": {"icon": "areaChart", "color": "#722ed1", "group": "技术分析", "order": 3, "form": {"layout": "vertical", "compact": false, "showDescription": true, "categories": [{"name": "基础设置", "label": "布林带基础参数", "collapsed": false}, {"name": "挤压设置", "label": "挤压形态识别", "collapsed": false}, {"name": "突破确认", "label": "突破信号确认", "collapsed": true}, {"name": "高级设置", "label": "高级功能", "collapsed": true}, {"name": "信号过滤", "label": "信号过滤条件", "collapsed": true}, {"name": "辅助指标", "label": "RSI辅助确认", "collapsed": true}]}, "chart": {"type": "candlestick", "indicators": [{"type": "bollinger", "params": ["bb_upper", "bb_mid", "bb_lower"], "colors": ["#ff4d4f", "#1890ff", "#52c41a"]}, {"type": "rsi", "params": ["rsi"], "subChart": true}, {"type": "volume", "params": ["volume", "volume_ratio"], "subChart": true}], "signals": {"buy": {"color": "#52c41a", "shape": "triangle-up"}, "sell": {"color": "#ff4d4f", "shape": "triangle-down"}}, "annotations": {"squeeze": {"color": "#faad14", "style": "dashed"}, "breakout": {"color": "#1890ff", "style": "solid"}}}}, "performance": {"complexity": "high", "dataIntensive": true, "cacheEnabled": true, "cacheTtl": 300, "realtime": true}, "risk": {"level": "medium", "factors": ["震荡市场容易产生假突破", "挤压后突破力度难以预测", "需要及时止损避免反转损失"], "suggestions": ["结合趋势指标确认方向", "严格执行止损策略", "关注成交量确认突破真实性"]}}