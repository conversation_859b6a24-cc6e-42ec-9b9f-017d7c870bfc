"""
统一WebSocket协议处理器
集成游戏交互、策略执行、库存管理和实时通信
"""
import json
import uuid
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect, Depends, Query
from pydantic import BaseModel, Field, ValidationError

from ..core.deps import get_current_user_ws
from ..services.inventory_service import InventoryService
from ..models.inventory import GameSession, InventoryManager
from ..models.user import User
from ..utils.response_utils import ResponseFormatter

logger = logging.getLogger(__name__)

class MessageType(str, Enum):
    """消息类型枚举"""
    GAME_SESSION = "game_session"
    STRATEGY_EXECUTION = "strategy_execution"
    INVENTORY = "inventory"
    MARKET_DATA = "market_data"
    GAME_EVENT = "game_event"
    AUTH = "auth"
    HEARTBEAT = "heartbeat"
    CONNECTION = "connection"
    ERROR = "error"

class QuantCardMessage(BaseModel):
    """统一消息格式"""
    type: MessageType
    subtype: Optional[str] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    strategy_id: Optional[str] = None
    group_id: Optional[str] = None
    template_id: Optional[str] = None
    payload: Dict[str, Any] = Field(default_factory=dict)
    timestamp: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    sequence: Optional[int] = None
    priority: Optional[str] = "normal"

class WebSocketConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 用户连接映射
        self.user_connections: Dict[str, List[WebSocket]] = {}
        # 会话连接映射
        self.session_connections: Dict[str, List[WebSocket]] = {}

    async def connect_user(self, websocket: WebSocket, user_id: str):
        """连接用户（包含accept）"""
        await websocket.accept()
        await self.register_connection(websocket, user_id)

    async def register_connection(self, websocket: WebSocket, user_id: str):
        """注册已接受的WebSocket连接"""
        if user_id not in self.user_connections:
            self.user_connections[user_id] = []
        self.user_connections[user_id].append(websocket)

        logger.info(f"用户 {user_id} WebSocket连接已建立，当前连接数: {len(self.user_connections[user_id])}")

    async def disconnect_user(self, websocket: WebSocket):
        """断开用户连接"""
        # 从所有映射中移除连接
        for user_id, connections in self.user_connections.items():
            if websocket in connections:
                connections.remove(websocket)
                if not connections:
                    del self.user_connections[user_id]
                logger.info(f"用户 {user_id} WebSocket连接已断开")
                break
                
        # 从会话连接中移除
        for session_id, connections in list(self.session_connections.items()):
            if websocket in connections:
                connections.remove(websocket)
                if not connections:
                    del self.session_connections[session_id]

    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """发送消息给特定用户"""
        if user_id in self.user_connections:
            dead_connections = []
            for websocket in self.user_connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception:
                    dead_connections.append(websocket)
            
            # 清理死连接
            for dead_ws in dead_connections:
                self.user_connections[user_id].remove(dead_ws)
                
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

    async def send_to_session(self, session_id: str, message: Dict[str, Any]):
        """发送消息给会话中的所有用户"""
        if session_id in self.session_connections:
            dead_connections = []
            for websocket in self.session_connections[session_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception:
                    dead_connections.append(websocket)
            
            # 清理死连接
            for dead_ws in dead_connections:
                self.session_connections[session_id].remove(dead_ws)
                
            if not self.session_connections[session_id]:
                del self.session_connections[session_id]

    async def broadcast_to_all(self, message: Dict[str, Any]):
        """广播消息给所有连接的用户"""
        all_connections = []
        for connections in self.user_connections.values():
            all_connections.extend(connections)
            
        dead_connections = []
        for websocket in all_connections:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception:
                dead_connections.append(websocket)
                
        # 清理死连接需要重新整理映射
        for dead_ws in dead_connections:
            await self.disconnect_user(dead_ws)

    async def handle_message(self, message: Dict[str, Any]):
        """处理来自内存队列的消息并广播给所有连接的用户"""
        try:
            # 广播消息给所有连接的用户
            await self.broadcast_to_all(message)
            logger.debug(f"已广播消息: {message.get('type', 'unknown')}")
        except Exception as e:
            logger.error(f"处理队列消息失败: {e}")

# 全局连接管理器
websocket_manager = WebSocketConnectionManager()

class UnifiedWebSocketHandler:
    """统一WebSocket消息处理器"""
    
    def __init__(self):
        self.message_handlers = {
            MessageType.GAME_SESSION: self.handle_game_session,
            MessageType.STRATEGY_EXECUTION: self.handle_strategy_execution,
            MessageType.INVENTORY: self.handle_inventory,
            MessageType.MARKET_DATA: self.handle_market_data,
            MessageType.GAME_EVENT: self.handle_game_event,
            MessageType.AUTH: self.handle_auth,
            MessageType.HEARTBEAT: self.handle_heartbeat,
        }

    async def route_message(self, websocket: WebSocket, raw_message: str, user_id: str):
        """统一消息路由"""
        try:
            # 解析消息
            message_data = json.loads(raw_message)
            message = QuantCardMessage(**message_data)
            
            # 设置用户ID（如果消息中没有）
            if not message.user_id:
                message.user_id = user_id
                
            # 获取处理器
            handler = self.message_handlers.get(message.type)
            if handler:
                response = await handler(websocket, message)
                if response:
                    await self.send_response(websocket, response)
            else:
                await self.send_error(websocket, "未知消息类型", message.request_id)
                
        except ValidationError as e:
            logger.error(f"消息格式验证失败: {e}")
            await self.send_error(websocket, f"消息格式错误: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            await self.send_error(websocket, "JSON格式错误")
        except Exception as e:
            logger.error(f"消息处理异常: {e}")
            await self.send_error(websocket, f"处理失败: {str(e)}")

    async def handle_game_session(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理游戏会话消息"""
        try:
            subtype = message.subtype
            user_id = message.user_id
            payload = message.payload

            if subtype == "join":
                # 创建游戏会话
                session_type = payload.get("session_type", "battle")
                strategy_group_id = payload.get("strategy_group_id")
                
                session_data = {"strategy_group_id": strategy_group_id} if strategy_group_id else {}
                result = await InventoryService.create_game_session(user_id, session_type, session_data)
                
                if result.get("success"):
                    # 将连接添加到会话映射
                    session_id = result["data"]["session_id"]
                    if session_id not in websocket_manager.session_connections:
                        websocket_manager.session_connections[session_id] = []
                    websocket_manager.session_connections[session_id].append(websocket)
                    
                    return {
                        "type": MessageType.GAME_SESSION,
                        "subtype": "joined",
                        "request_id": message.request_id,
                        "payload": result["data"]
                    }
                else:
                    return self.create_error_response(result.get("message", "创建会话失败"), message.request_id)
                    
            elif subtype == "leave":
                # 离开游戏会话
                session_id = message.session_id or payload.get("session_id")
                if session_id and session_id in websocket_manager.session_connections:
                    if websocket in websocket_manager.session_connections[session_id]:
                        websocket_manager.session_connections[session_id].remove(websocket)
                        
                    return {
                    "type": MessageType.GAME_SESSION,
                    "subtype": "left",
                        "request_id": message.request_id,
                    "payload": {"session_id": session_id}
                    }
                    
            elif subtype == "scene_change":
                # 场景切换
                scene = payload.get("scene")
                scene_data = payload.get("scene_data", {})
                
                # 这里可以添加场景切换逻辑
                return {
                    "type": MessageType.GAME_SESSION,
                    "subtype": "scene_changed",
                    "request_id": message.request_id,
                    "payload": {"scene": scene, "scene_data": scene_data}
                }
                
        except Exception as e:
            logger.error(f"处理游戏会话消息失败: {e}")
            return self.create_error_response(f"处理游戏会话失败: {str(e)}", message.request_id)

    async def handle_strategy_execution(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理策略执行消息"""
        try:
            subtype = message.subtype
            user_id = message.user_id
            payload = message.payload

            if subtype == "start":
                # 开始策略执行
                strategy_config = payload.get("strategy_config", {})
                execution_id = str(uuid.uuid4())
                
                # 这里可以集成真实的策略执行逻辑
                # 暂时返回模拟结果
                return {
                    "type": MessageType.STRATEGY_EXECUTION,
                    "subtype": "started",
                    "request_id": message.request_id,
                    "payload": {
                        "execution_id": execution_id,
                        "strategy_config": strategy_config,
                        "status": "running"
                    }
                }
                
            elif subtype == "stop":
                # 停止策略执行
                execution_id = payload.get("execution_id")
                return {
                    "type": MessageType.STRATEGY_EXECUTION,
                    "subtype": "stopped",
                    "request_id": message.request_id,
                    "payload": {"execution_id": execution_id}
                }
                
        except Exception as e:
            logger.error(f"处理策略执行消息失败: {e}")
            return self.create_error_response(f"处理策略执行失败: {str(e)}", message.request_id)

    async def handle_inventory(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理库存操作消息"""
        try:
            subtype = message.subtype
            user_id = message.user_id
            payload = message.payload

            if subtype == "list":
                # 获取库存列表
                available_only = payload.get("available_only", False)
                result = await InventoryService.get_user_inventory(user_id, available_only)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "list",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "add":
                # 添加卡牌到库存
                template_id = payload.get("template_id")
                quantity = payload.get("quantity", 1)
                source = payload.get("source", "game_reward")
                
                if not template_id:
                    return self.create_error_response("缺少template_id参数", message.request_id)
                    
                result = await InventoryService.add_cards_to_inventory(user_id, template_id, quantity, source)
                
                # 如果成功，广播库存更新给用户的所有连接
                if result.get("success"):
                    update_message = {
                        "type": MessageType.INVENTORY,
                        "subtype": "update",
                        "payload": {
                            "template_id": template_id,
                            "operation": "add",
                            "quantity": quantity,
                            "result": result["data"]
                        }
                    }
                    await websocket_manager.send_to_user(user_id, update_message)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "add",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "consume":
                # 消耗库存卡牌
                template_id = payload.get("template_id")
                quantity = payload.get("quantity", 1)
                reason = payload.get("reason", "game_action")
                
                if not template_id:
                    return self.create_error_response("缺少template_id参数", message.request_id)
                    
                result = await InventoryService.consume_cards_from_inventory(user_id, template_id, quantity, reason)
                
                # 如果成功，广播库存更新给用户的所有连接
                if result.get("success"):
                    update_message = {
                        "type": MessageType.INVENTORY,
                        "subtype": "update",
                        "payload": {
                            "template_id": template_id,
                            "operation": "consume",
                            "quantity": quantity,
                            "result": result["data"]
                        }
                    }
                    await websocket_manager.send_to_user(user_id, update_message)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "consume",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "check":
                # 检查卡牌可用性
                template_id = payload.get("template_id")
                required_quantity = payload.get("required_quantity", 1)
                
                if not template_id:
                    return self.create_error_response("缺少template_id参数", message.request_id)
                    
                result = await InventoryService.check_card_availability(user_id, template_id, required_quantity)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "check",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "transactions":
                # 获取交易历史
                template_id = payload.get("template_id")
                limit = payload.get("limit", 50)
                
                result = await InventoryService.get_inventory_transactions(user_id, template_id, limit)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "transactions",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "grant_starter":
                # 发放启动卡包
                result = await InventoryService.grant_starter_cards(user_id)
                
                # 如果成功，广播库存更新
                if result.get("success"):
                    update_message = {
                        "type": MessageType.INVENTORY,
                        "subtype": "update",
                        "payload": {
                            "operation": "grant_starter",
                            "result": result["data"]
                        }
                    }
                    await websocket_manager.send_to_user(user_id, update_message)
                
                return {
                    "type": MessageType.INVENTORY,
                    "subtype": "grant_starter",
                    "request_id": message.request_id,
                    "payload": result
                }
                
            elif subtype == "draw_card":
                # 抽卡功能
                count = payload.get("count", 1)
                pack_type = payload.get("pack_type", "standard")
                
                # 调用抽卡API逻辑
                try:
                    # 这里复用抽卡API的逻辑
                    import random
                    import uuid
                    from ..models.strategy_template import StrategyTemplate
                    
                    # 获取可抽取的策略模板
                    collection = await StrategyTemplate.get_collection()
                    templates = []
                    async for doc in collection.find({"is_active": True}):
                        doc['id'] = str(doc.pop('_id'))
                        templates.append(StrategyTemplate(**doc))
                    
                    if not templates:
                        return self.create_error_response("暂无可抽取的卡牌", message.request_id)
                    
                    # 抽卡概率配置
                    rarity_weights = {
                        "common": 60,
                        "rare": 25,
                        "epic": 12,
                        "legendary": 2.8,
                        "mythic": 0.2
                    }
                    
                    drawn_cards = []
                    transaction_id = str(uuid.uuid4())
                    
                    for _ in range(min(count, 10)):  # 限制最多10张
                        # 按权重随机选择稀有度
                        rarity_pool = []
                        for rarity, weight in rarity_weights.items():
                            rarity_pool.extend([rarity] * int(weight * 10))
                        
                        selected_rarity = random.choice(rarity_pool)
                        
                        # 从对应稀有度中随机选择卡牌
                        rarity_templates = []
                        for template in templates:
                            template_rarity = "common"
                            if "rare" in template.tags:
                                template_rarity = "rare"
                            elif "epic" in template.tags:
                                template_rarity = "epic"
                            elif "legendary" in template.tags:
                                template_rarity = "legendary"
                            elif "mythic" in template.tags:
                                template_rarity = "mythic"
                            
                            if template_rarity == selected_rarity:
                                rarity_templates.append(template)
                        
                        # 如果该稀有度没有卡牌，降级到普通卡牌
                        if not rarity_templates:
                            rarity_templates = [t for t in templates if "common" in t.tags or not any(r in t.tags for r in ["rare", "epic", "legendary", "mythic"])]
                        
                        if rarity_templates:
                            selected_template = random.choice(rarity_templates)
                            
                            # 添加到用户库存
                            from ..models.inventory import InventoryManager
                            await InventoryManager.add_cards_to_user(
                                user_id=user_id,
                                template_id=selected_template.template_id,
                                quantity=1,
                                source="card_draw",
                                transaction_id=transaction_id
                            )
                            
                            drawn_cards.append({
                                "template_id": selected_template.template_id,
                                "name": selected_template.name,
                                "description": selected_template.description,
                                "rarity": selected_rarity,
                                "icon": selected_template.ui.icon if selected_template.ui and selected_template.ui.icon else "📊",
                                "color": selected_template.ui.color if selected_template.ui and selected_template.ui.color else "#1890ff",
                                "is_new": True
                            })
                    
                    # 广播库存更新
                    if drawn_cards:
                        update_message = {
                            "type": MessageType.INVENTORY,
                            "subtype": "update",
                            "payload": {
                                "operation": "card_draw",
                                "drawn_cards": drawn_cards,
                                "transaction_id": transaction_id
                            }
                        }
                        await websocket_manager.send_to_user(user_id, update_message)
                    
                    return {
                        "type": MessageType.INVENTORY,
                        "subtype": "draw_card",
                        "request_id": message.request_id,
                        "payload": {
                            "success": True,
                            "message": f"成功抽取 {len(drawn_cards)} 张卡牌！",
                            "data": {
                                "drawn_cards": drawn_cards,
                                "transaction_id": transaction_id,
                                "draw_count": len(drawn_cards)
                            }
                        }
                    }
                    
                except Exception as e:
                    logger.error(f"抽卡功能失败: {e}")
                    return self.create_error_response(f"抽卡失败: {str(e)}", message.request_id)
                
        except Exception as e:
            logger.error(f"处理库存消息失败: {e}")
            return self.create_error_response(f"处理库存操作失败: {str(e)}", message.request_id)

    async def handle_market_data(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理市场数据消息"""
        try:
            # 这里可以添加市场数据处理逻辑
            return {
                "type": MessageType.MARKET_DATA,
                "subtype": "response",
                "request_id": message.request_id,
                "payload": {"status": "market_data_not_implemented"}
            }
        except Exception as e:
            logger.error(f"处理市场数据消息失败: {e}")
            return self.create_error_response(f"处理市场数据失败: {str(e)}", message.request_id)

    async def handle_game_event(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理游戏事件消息"""
        try:
            # 游戏事件通常是单向的，不需要响应
            logger.info(f"收到游戏事件: {message.subtype}, 用户: {message.user_id}")
            return None
        except Exception as e:
            logger.error(f"处理游戏事件消息失败: {e}")
            return self.create_error_response(f"处理游戏事件失败: {str(e)}", message.request_id)

    async def handle_auth(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理认证消息"""
        try:
            subtype = message.subtype

            if subtype == "guest_login":
                # 访客登录
                guest_id = f"guest_{uuid.uuid4().hex[:8]}"
                return {
                    "type": MessageType.AUTH,
                    "subtype": "guest_login_success",
                    "request_id": message.request_id,
                    "payload": {
                        "guest_id": guest_id,
                        "permissions": ["browse", "view_worldmap"]
                    }
                }

        except Exception as e:
            logger.error(f"处理认证消息失败: {e}")
            return self.create_error_response(f"处理认证失败: {str(e)}", message.request_id)

    async def handle_heartbeat(self, websocket: WebSocket, message: QuantCardMessage) -> Optional[Dict[str, Any]]:
        """处理心跳消息"""
        return {
            "type": MessageType.HEARTBEAT,
            "subtype": "pong",
            "request_id": message.request_id,
            "payload": {"server_time": datetime.utcnow().isoformat()}
        }

    async def send_response(self, websocket: WebSocket, response: Dict[str, Any]):
        """发送响应消息"""
        try:
            await websocket.send_text(json.dumps(response))
        except Exception as e:
            logger.error(f"发送响应失败: {e}")

    async def send_error(self, websocket: WebSocket, error_message: str, request_id: str = None):
        """发送错误消息"""
        error_response = {
            "type": MessageType.ERROR,
            "request_id": request_id,
            "payload": {"error": error_message},
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_response(websocket, error_response) 

    def create_error_response(self, error_message: str, request_id: str = None) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "type": MessageType.ERROR,
            "request_id": request_id,
            "payload": {"error": error_message},
            "timestamp": datetime.utcnow().isoformat()
        } 