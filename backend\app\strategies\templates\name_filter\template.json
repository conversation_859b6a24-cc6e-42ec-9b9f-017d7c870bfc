{"template_id": "name_filter", "name": "股票名称", "description": "根据股票名称中包含或不包含的关键字筛选股票   <span style='color: #808080'>例：筛选名称中包含'银行'的股票。</span>", "version": "1.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "名称包含或不包含关键字", "required": true, "options": [{"label": "包含", "value": "包含"}, {"label": "不包含", "value": "不包含"}], "default": "包含", "group": "name_filter"}, "keywords": {"type": "string", "label": "关键字", "description": "多个关键字用逗号分隔", "required": true, "default": "", "group": "name_filter"}}, "parameterGroups": {"name_filter": {"parameters": ["operator", "keywords"], "displayMode": "inline", "prefix": "名称", "separator": "", "layout": "horizontal"}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"代码": {"type": "string"}, "名称": {"type": "string"}}}}}, "ui": {"icon": "tag", "color": "#722ed1", "group": "筛选", "order": 2, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}