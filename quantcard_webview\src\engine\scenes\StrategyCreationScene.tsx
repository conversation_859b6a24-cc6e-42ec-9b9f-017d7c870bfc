/**
 * 🎯 策略创建工坊 - Strategy Creation Workshop
 * 重新设计的策略创建界面，集成可折叠库存区域、策略组配置、参数编辑等功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useCardsState } from '../../store/hooks/useCardsState';
import { useStrategyState } from '../../store/hooks/useStrategyState';
import { UniversalCard } from '../components/UniversalCard';
import type { UnifiedCardData } from '../components/UniversalCard';
import type { StrategyCardInstance, StrategyGroup } from '../../types/game';
import ParameterEditor, { type ParameterConfigItem } from '../components/ParameterEditor'
import SaveStrategyPanel from '../components/SaveStrategyPanel'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

// 🎨 主容器内容区域
const CreationContent = styled.div`
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: 1;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 25%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
`;

// 🎮 工具栏区域
const ToolBarArea = styled.div`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  flex-shrink: 0;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
`;

const ToolBarLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  @media (max-width: 768px) {
    flex: 1;
    min-width: 200px;
  }
`;

const ToolBarRight = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
`;

const StatsDisplay = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  
  @media (max-width: 640px) {
    gap: 0.5rem;
    font-size: 0.8rem;
  }
`

const ActionButton = styled(motion.button)<{ $variant?: 'primary' | 'secondary' | 'success' | 'danger' }>`
  padding: 0.5rem 1rem;
  border: 2px solid ${props => {
    switch(props.$variant) {
      case 'primary': return '#3b82f6'
      case 'success': return '#10b981'
      case 'danger': return '#ef4444'
      default: return '#64748b'
    }
  }};
  background: ${props => {
    switch(props.$variant) {
      case 'primary': return '#3b82f6'
      case 'success': return '#10b981'
      case 'danger': return '#ef4444'
      default: return 'transparent'
    }
  }};
  color: ${props => {
    switch(props.$variant) {
      case 'primary':
      case 'success':
      case 'danger': return 'white'
      default: return '#64748b'
    }
  }};
  border-radius: 8px;
  font-family: "Inter", sans-serif;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px ${props => {
      switch(props.$variant) {
        case 'primary': return 'rgba(59, 130, 246, 0.3)'
        case 'success': return 'rgba(16, 185, 129, 0.3)'
        case 'danger': return 'rgba(239, 68, 68, 0.3)'
        default: return 'rgba(100, 116, 139, 0.2)'
      }
    }};
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  @media (max-width: 640px) {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
`;

const ModeSelector = styled.div`
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 2px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  
  @media (max-width: 640px) {
    order: -1;
    width: 100%;
  }
`;

const ModeOption = styled(motion.button)<{ $active: boolean }>`
  padding: 0.5rem 1rem;
  border: none;
  background: ${props => props.$active ? '#3b82f6' : 'transparent'};
  color: ${props => props.$active ? 'white' : '#64748b'};
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${props => props.$active ? '#2563eb' : 'rgba(59, 130, 246, 0.1)'};
  }
`;

// 📋 主工作区 - 新布局
const WorkArea = styled.div`
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
`;

// 📦 库存区域 - 可折叠的50%屏幕区域（折叠时仅显示标签）
const InventoryArea = styled(motion.div)<{ $isExpanded: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: ${props => props.$isExpanded ? '50vh' : '0px'};
  background: ${props => props.$isExpanded ? 'linear-gradient(135deg, #3b2f8b 0%, #6b46c1 25%, #7c3aed 50%, #8b5cf6 75%, #a855f7 100%)' : 'transparent'};
  border-radius: ${props => props.$isExpanded ? '20px 20px 0 0' : '0'};
  box-shadow: ${props => props.$isExpanded ? '0 -4px 32px rgba(139, 92, 246, 0.3)' : 'none'};
  z-index: 200;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
`;

const InventoryToggle = styled(motion.button)`
  position: absolute;
  top: -40px;
  left: 16px;
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 20px 20px 0 0;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 -2px 8px rgba(139, 92, 246, 0.3);
  pointer-events: auto;
  
  &:hover {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6);
    transform: translateY(-2px);
  }
`;

const InventoryHeader = styled.div<{ $isExpanded: boolean }>`
  padding: ${props => props.$isExpanded ? '1.5rem 1.5rem 1rem' : '0'};
  background: ${props => props.$isExpanded ? 'rgba(255, 255, 255, 0.1)' : 'transparent'};
  backdrop-filter: ${props => props.$isExpanded ? 'blur(10px)' : 'none'};
  border-bottom: ${props => props.$isExpanded ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'};
  display: ${props => props.$isExpanded ? 'flex' : 'none'};
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
`;

const InventoryTitle = styled.h3<{ $isExpanded: boolean }>`
  color: #ffffff;
  font-size: ${props => props.$isExpanded ? '1.25rem' : '1rem'};
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: '🎒';
    font-size: ${props => props.$isExpanded ? '1.5rem' : '1.25rem'};
  }
`;

const InventoryStats = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

const StatBadge = styled.div<{ $color: string }>`
  background: ${props => props.$color}20;
  border: 1px solid ${props => props.$color};
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const InventoryContent = styled.div<{ $isExpanded: boolean }>`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.$isExpanded ? '1rem 1.5rem' : '0'};
  opacity: ${props => props.$isExpanded ? 1 : 0};
  pointer-events: ${props => props.$isExpanded ? 'auto' : 'none'};
  transition: opacity 0.3s ease;
  display: ${props => props.$isExpanded ? 'block' : 'none'};
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }
`;

const InventoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 6px;
  justify-content: center;
`;

// 🃏 库存卡片包装器
const InventoryCardWrapper = styled(motion.div)<{ $available: boolean }>`
  position: relative;
  opacity: ${props => props.$available ? 1 : 0.6};
  cursor: ${props => props.$available ? 'grab' : 'not-allowed'};
  
  &:active {
    cursor: ${props => props.$available ? 'grabbing' : 'not-allowed'};
  }
  
  ${props => !props.$available && `
    &::after {
      content: '已用完';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(239, 68, 68, 0.9);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.7rem;
      font-weight: 600;
      z-index: 10;
    }
  `}
`;

const CardAnchor = styled.div`
  position: relative;
  display: inline-block;
`;

const QuantityBadge = styled.div<{ $count: number }>`
  position: absolute;
  top: 2px;
  right: 2px;
  background: ${props => props.$count > 0 ? '#10b981' : '#ef4444'};
  color: white;
  border-radius: 10px;
  height: 20px;
  padding: 0 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  font-weight: 700;
  border: 2px solid #ffffff;
  z-index: 15;
  line-height: 1;
`;

// 🎯 策略配置区域 - 主工作区
const ConfigurationArea = styled.div`
  flex: 1;
  background: rgba(248, 250, 252, 0.8);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 60px; /* 为折叠标签留空间 */
`;

const ConfigGrid = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
  background-size: 24px 24px;
  opacity: 0.6;
`;

const ConfigContent = styled.div`
  position: relative;
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
`;

const GroupHeader = styled.div`
  display: none;
`;

const GroupEditableTitle = styled.input`
  background: transparent;
  border: 1px solid transparent;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: inherit;
  min-width: 200px;
  
  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const GroupEditableDescription = styled.textarea`
  background: transparent;
  border: 1px solid transparent;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 400;
  margin: 0.5rem 0 0 0;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: inherit;
  resize: none;
  min-height: 40px;
  width: 100%;
  
  &:hover {
    border-color: rgba(59, 130, 246, 0.3);
    background: rgba(59, 130, 246, 0.05);
  }
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const GroupActions = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

// 🎴 策略卡片区域
const CardsArea = styled.div`
  min-height: clamp(260px, 40vh, 520px);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.02);
  position: relative;
  transition: all 0.3s ease;
  
  &.drag-over {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.08);
    border-style: solid;
  }
`;

const CardsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(115px, 1fr));
  gap: 6px;
  min-height: 120px;
`;

const EmptyCardsHint = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #64748b;
  pointer-events: none;
  
  .icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
  }
  
  .title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .subtitle {
    font-size: 0.875rem;
    opacity: 0.8;
  }
`;

const ConfigCardWrapper = styled(motion.div)<{ $isSelected?: boolean }>`
  position: relative;
  cursor: pointer;
  
  ${props => props.$isSelected && `
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid #3b82f6;
      border-radius: 20px;
      background: rgba(59, 130, 246, 0.1);
      z-index: -1;
    }
  `}
`;

const CardRemoveButton = styled(motion.button)`
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  border: 2px solid white;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: bold;
  z-index: 20;
  line-height: 1;
  
  &:hover {
    background: #dc2626;
    transform: scale(1.05);
  }
`;

// 🎯 策略创建主组件（简化结构）
function StrategyCreationScene({ sceneData }: { sceneData?: any }) {
  const { inventory, loading: inventoryLoading, loadInventory } = useCardsState();
  const { groups, createGroup, addCardToGroup, currentGroupId, setCurrentGroup } = useStrategyState();
  
  // 🎮 组件状态
  const [isInventoryExpanded, setIsInventoryExpanded] = useState(true);
  const [currentGroup, setCurrentGroupState] = useState<StrategyGroup | null>(null);
  const [executionMode, setExecutionMode] = useState<'sequential' | 'parallel'>('sequential');
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);
  const [editingCard, setEditingCard] = useState<StrategyCardInstance | null>(null);

  const [isDragOver, setIsDragOver] = useState(false);
  const [draggedCard, setDraggedCard] = useState<UnifiedCardData | null>(null);
  const [isSavePanelOpen, setIsSavePanelOpen] = useState(false);
  
  // 📊 统计信息
  const inventoryStats = React.useMemo(() => {
    const totalCards = inventory.reduce((sum, item) => sum + item.quantity, 0);
    const availableCards = inventory.filter(item => item.quantity > 0).length;
    return { totalCards, availableCards };
  }, [inventory]);
  
  // 🎯 初始化
  useEffect(() => {
    if (inventory.length === 0) {
      loadInventory();
    }
  }, []);
  
  useEffect(() => {
    if (!currentGroup) {
      setCurrentGroupState({
        id: 'temp_group',
        name: '未命名策略',
        description: '',
        cards: [],
        execution_mode: executionMode,
        status: 'inactive',
        performance: {
          total_return: 0,
          win_rate: 0,
          max_drawdown: 0,
          sharpe_ratio: 0
        },
        created_at: new Date(),
        updated_at: new Date()
      });
    }
  }, [executionMode]);
  
  // 🎴 处理卡片添加
  const handleAddCard = useCallback((cardData: UnifiedCardData) => {
    if (!currentGroup) return;
    
    const newCard: StrategyCardInstance = {
      id: `card_${Date.now()}`,
      template_id: (cardData as any).id,
      parameters: {},
      position: { x: 0, y: 0 },
      created_at: new Date(),
      order: currentGroup.cards.length
    };
    
    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: [...prev.cards, newCard],
      updated_at: new Date()
    } : null);
    
    // 保持卡包展开（不自动收回）
  }, [currentGroup]);
  
  // 🖱️ 拖拽处理函数（宽松事件类型以兼容 motion.div）
  const handleDragStart = useCallback((e: any, cardData: UnifiedCardData) => {
    setDraggedCard(cardData);
    if (e?.dataTransfer) {
      e.dataTransfer.effectAllowed = 'copy';
      e.dataTransfer.setData('text/plain', (cardData as any).id);
    }
  }, []);
  
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    setIsDragOver(true);
  }, []);
  
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);
  
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (draggedCard) {
      handleAddCard(draggedCard);
      setDraggedCard(null);
    }
  }, [draggedCard, handleAddCard]);
  
  // 🗑️ 处理卡片移除
  const handleRemoveCard = useCallback((cardId: string) => {
    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: prev.cards.filter(card => card.id !== cardId),
      updated_at: new Date()
    } : null);
  }, []);
  
  // ⚙️ 处理参数编辑
  const handleEditCard = useCallback((card: StrategyCardInstance) => {
    setEditingCard(card);
  }, []);
  
  const handleSaveParameters = useCallback((parameters: Record<string, any>) => {
    if (!editingCard || !currentGroup) return;
    
    setCurrentGroupState(prev => prev ? {
      ...prev,
      cards: prev.cards.map(card => 
        card.id === editingCard.id 
          ? { ...card, parameters }
          : card
      ),
      updated_at: new Date()
    } : null);
    
    setEditingCard(null);
  }, [editingCard, currentGroup]);
  
  // 📝 获取卡片参数配置（类型严格）
  const getCardParameterConfig = useCallback((templateId: string): ParameterConfigItem[] => {
    const parameterConfigs: Record<string, ParameterConfigItem[]> = {
      'ma_cross': [
        { key: 'short_period', label: '短期周期', type: 'number', defaultValue: 5, min: 1, max: 100 },
        { key: 'long_period', label: '长期周期', type: 'number', defaultValue: 20, min: 1, max: 200 },
        { key: 'signal_type', label: '信号类型', type: 'select', defaultValue: 'golden_cross', options: ['golden_cross', 'death_cross', 'both'] }
      ],
      'rsi_oversold': [
        { key: 'period', label: 'RSI周期', type: 'number', defaultValue: 14, min: 2, max: 100 },
        { key: 'overbought_level', label: '超买线', type: 'number', defaultValue: 70, min: 50, max: 90 },
        { key: 'oversold_level', label: '超卖线', type: 'number', defaultValue: 30, min: 10, max: 50 }
      ],
      'volume_breakout': [
        { key: 'volume_threshold', label: '成交量倍数', type: 'number', defaultValue: 2.0, min: 1.0, max: 10.0 },
        { key: 'lookback_period', label: '回望周期', type: 'number', defaultValue: 20, min: 5, max: 100 },
        { key: 'price_change_threshold', label: '价格变动阈值(%)', type: 'number', defaultValue: 5, min: 1, max: 20 }
      ],
      'market_cap_filter': [
        { key: 'min_market_cap', label: '最小市值(亿)', type: 'number', defaultValue: 100, min: 10, max: 10000 },
        { key: 'max_market_cap', label: '最大市值(亿)', type: 'number', defaultValue: 5000, min: 100, max: 50000 },
        { key: 'exclude_penny_stocks', label: '排除仙股', type: 'boolean', defaultValue: true }
      ]
    };
    
    const normalizedId = templateId.toLowerCase().replace(/[^a-z_]/g, '_');
    for (const configKey of Object.keys(parameterConfigs)) {
      if (normalizedId.includes(configKey) || configKey.includes(normalizedId.split('_')[0])) {
        return parameterConfigs[configKey];
      }
    }
    
    return [
      { key: 'param1', label: '参数1', type: 'number', defaultValue: 10, min: 1, max: 100 },
      { key: 'param2', label: '参数2', type: 'text', defaultValue: '默认值' },
      { key: 'enabled', label: '启用', type: 'boolean', defaultValue: true }
    ];
  }, []);
  
  // 💾 保存策略组
  const handleSaveGroup = useCallback(() => {
    if (!currentGroup || currentGroup.cards.length === 0) {
      alert('⚠️ 请先添加策略卡片再保存');
      return;
    }
    setIsSavePanelOpen(true);
  }, [currentGroup]);
  
  // 🧪 调试策略组
  const handleDebugGroup = useCallback(() => {
    if (!currentGroup || currentGroup.cards.length === 0) {
      alert('⚠️ 请先添加策略卡片再调试');
      return;
    }
    
    const modeDescriptions = {
      sequential: '顺序模式：策略卡片按顺序执行，前一个的输出作为后一个的输入',
      parallel: '并行模式：所有策略卡片同时执行，结果独立汇总'
    } as const;
    
    // 创建调试信息
    const debugInfo = [
      `🧪 策略调试报告`,
      ``,
      `📊 基本信息：`,
      `- 策略名称：${currentGroup.name}`,
      `- 卡片数量：${currentGroup.cards.length} 张`,
      `- 执行模式：${currentGroup.execution_mode}`,
      ``,
      `📋 模式说明：`,
      `${modeDescriptions[currentGroup.execution_mode as keyof typeof modeDescriptions] || '未知模式'}`,
      ``,
      `🎴 卡片列表：`,
      ...currentGroup.cards.map((card, index) => `${index + 1}. ${card.template_id.replace(/_/g, ' ')}`),
      ``,
      `✅ 调试检查完成！策略配置正常。`
    ].join('\n');
    
    alert(debugInfo);
  }, [currentGroup]);

  const handleConfirmSave = useCallback(async (name: string, description: string) => {
    if (!currentGroup) return;

    const hasIncompleteParameters = currentGroup.cards.some(card => {
      const config = getCardParameterConfig(card.template_id);
      return config.some(param =>
        card.parameters[param.key] === undefined ||
        card.parameters[param.key] === null ||
        card.parameters[param.key] === ''
      );
    });

    if (hasIncompleteParameters) {
      const proceed = confirm('⚠️ 部分策略卡片的参数未完整配置，是否继续保存？');
      if (!proceed) return;
    }

    try {
      const groupId = await createGroup(name, description);
      for (const card of currentGroup.cards) {
        await addCardToGroup(groupId, card);
      }
      setCurrentGroup(groupId);
      setIsSavePanelOpen(false);
      alert(`✅ 策略组保存成功！\n\n🆔 组ID：${groupId}\n📛 名称：${name}\n🎴 包含 ${currentGroup.cards.length} 张策略卡片\n⚙️ 执行模式：${currentGroup.execution_mode}`);
    } catch (error) {
      alert('❌ 保存失败：' + (error as Error).message);
    }
  }, [currentGroup, createGroup, addCardToGroup, setCurrentGroup, getCardParameterConfig]);

  return (
    <UnifiedMobileNav title="策略工坊" showBottomNav={false} showUserInfo={false}>
      <CreationContent>
        {/* 工具栏区域 */}
        <ToolBarArea>
          <ToolBarLeft>
            <StatsDisplay>
              <span>💎 {inventoryStats.availableCards} 可用</span>
              <span>📦 总计 {inventoryStats.totalCards}</span>
            </StatsDisplay>
          </ToolBarLeft>
          
          <ToolBarRight>
            <ModeSelector>
              <ModeOption
                $active={executionMode === 'sequential'}
                onClick={() => setExecutionMode('sequential')}
                whileTap={{ scale: 0.95 }}
              >
                顺序执行
              </ModeOption>
              <ModeOption
                $active={executionMode === 'parallel'}
                onClick={() => setExecutionMode('parallel')}
                whileTap={{ scale: 0.95 }}
              >
                并行执行
              </ModeOption>
            </ModeSelector>
            
            <ActionButton
              $variant="secondary"
              onClick={handleDebugGroup}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={!currentGroup || currentGroup.cards.length === 0}
            >
              <span>🧪</span>
              <span>调试</span>
            </ActionButton>
            
            <ActionButton
              $variant="success"
              onClick={() => setIsSavePanelOpen(true)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={!currentGroup || currentGroup.cards.length === 0}
            >
              <span>💾</span>
              <span>保存</span>
            </ActionButton>
          </ToolBarRight>
        </ToolBarArea>

        {/* 工作区域 */}
        <WorkArea>
          {/* 策略配置面板 */}
          <ConfigurationArea>
            <ConfigGrid />
            <ConfigContent>
              {/* 策略组配置区 */}
              <GroupHeader>
                <div style={{ flex: 1 }} />
                <GroupActions>
                <StatBadge $color="#3b82f6">
                  {currentGroup?.cards.length || 0} 张卡片
                </StatBadge>
                <StatBadge $color={
                  executionMode === 'sequential' ? '#10b981' : '#f59e0b'
                }>
                  {executionMode === 'sequential' ? '📋 串行' : '🚀 并行'}
                </StatBadge>
              </GroupActions>
            </GroupHeader>
            
            {/* 策略卡片区域 */}
            <CardsArea 
              className={isDragOver ? 'drag-over' : ''}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {currentGroup && currentGroup.cards.length > 0 ? (
                <CardsGrid>
                  <AnimatePresence mode="popLayout">
                    {currentGroup.cards.map((card, index) => {
                      const templateData = inventory.find(item => item.template_id === card.template_id);
                      if (!templateData) return null;
                      
                      const cardData: UnifiedCardData = {
                        id: card.template_id as any,
                        name: templateData.template_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                        description: `${templateData.template_id} 策略卡牌`,
                        category: 'timing',
                        rarity: 'common',
                        power: Math.floor(Math.random() * 100) + 20,
                        defense: Math.floor(Math.random() * 100) + 20,
                        level: Math.floor(Math.random() * 10) + 1
                      } as any;
                      
                      return (
                        <motion.div
                          key={card.id}
                          initial={{ opacity: 0, scale: 0.8, y: 20 }}
                          animate={{ opacity: 1, scale: 1, y: 0 }}
                          exit={{ opacity: 0, scale: 0.8, y: -20 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          layout
                        >
                          <ConfigCardWrapper
                            $isSelected={selectedCardId === card.id}
                            onClick={() => handleEditCard(card)}
                          >
                            <CardAnchor>
                              <UniversalCard
                                card={cardData}
                                variant="collection"
                                size="sm"
                                interactive={true}
                                showStats={true}
                                showLevel={true}
                              />
                              <CardRemoveButton
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveCard(card.id);
                                }}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                −
                              </CardRemoveButton>
                            </CardAnchor>
                          </ConfigCardWrapper>
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>
                </CardsGrid>
              ) : (
                <EmptyCardsHint
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="icon">🎯</div>
                  <div className="title">开始构建您的策略</div>
                  <div className="subtitle">拖动卡片到这个区域，点击卡片配置参数</div>
                </EmptyCardsHint>
              )}
            </CardsArea>
          </ConfigContent>
        </ConfigurationArea>
      </WorkArea>

      {/* 库存区域 */}
      <InventoryArea 
        $isExpanded={isInventoryExpanded}
        initial={false}
        animate={isInventoryExpanded ? 'expanded' : 'collapsed'}
      >
        <InventoryToggle
          onClick={() => setIsInventoryExpanded(!isInventoryExpanded)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span>{isInventoryExpanded ? '收起卡包' : '展开卡包'}</span>
        </InventoryToggle>
        
        <InventoryHeader $isExpanded={isInventoryExpanded}>
          <InventoryTitle $isExpanded={isInventoryExpanded}>我的卡牌</InventoryTitle>
          <InventoryStats>
            <StatBadge $color="#10b981">{inventoryStats.totalCards}</StatBadge>
            <StatBadge $color="#f59e0b">{inventoryStats.availableCards}</StatBadge>
          </InventoryStats>
        </InventoryHeader>
        
        <InventoryContent $isExpanded={isInventoryExpanded}>
          {inventory.length > 0 ? (
            <InventoryGrid>
              <AnimatePresence mode="popLayout">
                {inventory.map((item, index) => {
                  const cardData: UnifiedCardData = {
                    id: item.template_id as any,
                    name: item.template_id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    description: `${item.template_id} 策略卡牌`,
                    category: 'timing',
                    rarity: 'common',
                    power: Math.floor(Math.random() * 100) + 20,
                    defense: Math.floor(Math.random() * 100) + 20,
                    level: Math.floor(Math.random() * 10) + 1
                  } as any;
                  
                  return (
                    <motion.div
                      key={item.template_id}
                      initial={{ opacity: 0, scale: 0.8, y: 20 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.8, y: -20 }}
                      transition={{ duration: 0.3, delay: index * 0.02 }}
                      layout
                    >
                      <InventoryCardWrapper 
                        $available={item.quantity > 0}
                        draggable={item.quantity > 0}
                        onDragStart={(e) => { if (item.quantity > 0) handleDragStart(e, cardData) }}
                        onClick={() => item.quantity > 0 && handleAddCard(cardData)}
                      >
                        <CardAnchor>
                          <UniversalCard
                            card={cardData}
                            variant="inventory"
                            size="sm"
                            interactive={item.quantity > 0}
                            disabled={item.quantity === 0}
                            showStats={true}
                            showLevel={true}
                          />
                          <QuantityBadge $count={item.quantity}>
                            ×{item.quantity}
                          </QuantityBadge>
                        </CardAnchor>
                      </InventoryCardWrapper>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </InventoryGrid>
          ) : (
            <EmptyCardsHint
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="icon">📦</div>
              <div className="title">库存为空</div>
              <div className="subtitle">前往商店获取策略卡片</div>
            </EmptyCardsHint>
          )}
        </InventoryContent>
      </InventoryArea>

      {/* 参数编辑器（外部组件） */}
      <ParameterEditor
        card={editingCard as any}
        parameterConfig={editingCard ? getCardParameterConfig(editingCard.template_id) : []}
        onSave={handleSaveParameters}
        onCancel={() => setEditingCard(null)}
      />
      <SaveStrategyPanel
        open={isSavePanelOpen}
        initialName={currentGroup?.name ?? '未命名策略'}
        initialDescription={currentGroup?.description ?? ''}
        onCancel={() => setIsSavePanelOpen(false)}
        onConfirm={handleConfirmSave}
      />
      </CreationContent>
    </UnifiedMobileNav>
  );
}

export default StrategyCreationScene;