from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pydantic import Field, BaseModel
from bson import ObjectId

from ..core.data.db.base import MongoModel, db_manager

class ParameterValidation(BaseModel):
    """参数验证规则"""
    min: Optional[float] = None
    max: Optional[float] = None
    step: Optional[float] = None
    pattern: Optional[str] = None

class ParameterConfig(BaseModel):
    """参数配置"""
    type: str
    label: Optional[str] = None
    description: Optional[str] = None
    required: bool = False
    default: Optional[Any] = None
    options: Optional[List[Dict[str, Any]]] = None
    validation: Optional[ParameterValidation] = None
    unit: Optional[str] = None
    group: Optional[str] = None
    order: Optional[int] = None
    visibleWhen: Optional[Dict[str, Any]] = None

class ParameterGroup(BaseModel):
    """参数组配置"""
    parameters: List[str]
    displayMode: str = "inline"
    prefix: Optional[str] = None
    separator: Optional[str] = None
    layout: str = "horizontal"

class UiConfig(BaseModel):
    """UI配置"""
    icon: Optional[str] = None
    color: Optional[str] = None
    group: Optional[str] = None
    order: Optional[int] = None
    form: Optional[Dict[str, Any]] = None

class StrategyTemplate(MongoModel, BaseModel):
    """策略模板模型"""
    template_id: str = Field(...)  # 策略模板ID，唯一标识符
    name: str = Field(...)  # 策略名称
    description: str = Field(default="")  # 策略描述
    # type 字段已移除，使用 template_id 作为唯一标识符
    version: str = Field(default="1.0.0")  # 策略版本
    author: str = Field(default="system")  # 作者
    stars: Optional[int] = Field(default=3)  # 星级评分
    tags: List[str] = Field(default_factory=list)  # 标签，用于分类和筛选，包括功能标记（选股/择时/回测）
    parameters: Dict[str, ParameterConfig] = Field(default_factory=dict)  # 参数配置
    parameterGroups: Optional[Dict[str, ParameterGroup]] = Field(default_factory=dict)  # 参数组配置
    ui: Optional[UiConfig] = None  # UI配置
    is_active: bool = Field(default=True)  # 是否激活
    template_code: Optional[str] = Field(default=None)  # 策略代码文件路径
    created_at: datetime = Field(default_factory=datetime.utcnow)  # 创建时间
    updated_at: datetime = Field(default_factory=datetime.utcnow)  # 更新时间

    @classmethod
    def get_collection_name(cls) -> str:
        """获取集合名称"""
        return "strategy_templates"

    @classmethod
    def get_db_name(cls) -> str:
        """获取数据库名称"""
        return "quantcard"

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            ObjectId: str
        }

    @classmethod
    async def find_by_template_id(cls, template_id: str) -> Optional["StrategyTemplate"]:
        """按模板ID查找策略模板 (推荐方法)"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        result = await db[cls.get_collection_name()].find_one({"template_id": template_id})
        if result:
            return cls(**result)
        return None

    @classmethod
    async def find_all(cls) -> List["StrategyTemplate"]:
        """查找所有策略模板"""
        db = await db_manager.get_mongodb_database(cls.get_db_name())
        cursor = db[cls.get_collection_name()].find()
        return [cls(**doc) async for doc in cursor]

    async def save_to_db(self):
        """保存到数据库"""
        db = await db_manager.get_mongodb_database(self.get_db_name())
        data = self.dict(exclude={"id"})
        if hasattr(self, "id") and self.id:
            # 更新
            await db[self.get_collection_name()].update_one(
                {"_id": ObjectId(self.id)},
                {"$set": data}
            )
        else:
            # 创建
            result = await db[self.get_collection_name()].insert_one(data)
            self.id = result.inserted_id
        return self