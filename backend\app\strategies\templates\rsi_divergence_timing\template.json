{"id": "rsi_divergence_timing", "name": "RSI背离择时策略", "description": "基于RSI指标与价格背离的专业择时策略，在超买超卖区域捕捉反转机会", "version": "1.0.0", "author": "QuantCard团队", "tags": ["择时", "RSI背离", "技术指标", "超买超卖", "反转信号"], "createTime": "2024-01-01T00:00:00Z", "updateTime": "2024-01-01T00:00:00Z", "parameters": {"rsi_period": {"label": "RSI计算周期", "type": "number", "default": 14, "min": 7, "max": 30, "required": true, "placeholder": "请输入RSI周期", "description": "RSI指标的计算周期，标准为14", "unit": "分钟"}, "oversold_level": {"label": "RSI超卖线", "type": "number", "default": 30.0, "min": 20.0, "max": 40.0, "required": true, "placeholder": "请输入超卖阈值", "description": "RSI低于此值认为超卖，标准为30", "unit": ""}, "overbought_level": {"label": "RSI超买线", "type": "number", "default": 70.0, "min": 60.0, "max": 80.0, "required": true, "placeholder": "请输入超买阈值", "description": "RSI高于此值认为超买，标准为70", "unit": ""}, "min_divergence_strength": {"label": "最小背离强度", "type": "number", "default": 5.0, "min": 3.0, "max": 15.0, "required": true, "placeholder": "请输入背离强度", "description": "背离信号的最小强度要求", "unit": ""}}, "outputs": [{"name": "signals", "type": "signal", "description": "RSI背离交易信号", "fields": ["symbol", "name", "direction", "trigger_condition", "rsi_value", "divergence_strength"]}], "modes": [{"name": "timing", "label": "择时模式", "description": "实时监控RSI背离信号"}, {"name": "backtest", "label": "回测模式", "description": "使用历史数据验证背离策略"}], "supportedDataSources": ["questdb"], "examples": [{"name": "标准RSI背离", "description": "经典的RSI背离参数设置", "parameters": {"rsi_period": 14, "oversold_level": 30.0, "overbought_level": 70.0, "min_divergence_strength": 5.0}}], "template_id": "timing", "ui": {"icon": "lineChart", "color": "#fa541c", "group": "择时", "order": 2}}