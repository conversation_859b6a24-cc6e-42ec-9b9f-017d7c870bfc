"""
库存管理服务
提供用户库存的CRUD操作、实时同步和业务逻辑封装
"""
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging

from ..models.inventory import InventoryItem, InventoryTransaction, InventoryManager, GameSession
from ..models.user import User
from ..utils.response_utils import ResponseFormatter

logger = logging.getLogger(__name__)

class InventoryService:
    """库存服务类 - 封装所有库存相关业务逻辑"""
    
    @staticmethod
    async def get_user_inventory(user_id: str, available_only: bool = False) -> Dict[str, Any]:
        """获取用户库存"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 获取库存摘要
            summary = await InventoryManager.get_user_inventory_summary(user_id)
            
            # 如果只要可用物品，过滤掉数量为0的
            if available_only:
                summary["items"] = [item for item in summary["items"] if item["quantity"] > 0]
                
            return ResponseFormatter.success(summary, "获取库存成功")
            
        except Exception as e:
            logger.error(f"获取用户库存失败: {e}")
            return ResponseFormatter.error(f"获取库存失败: {str(e)}")

    @staticmethod
    async def add_cards_to_inventory(user_id: str, template_id: str, quantity: int, 
                                   source: str = "activity") -> Dict[str, Any]:
        """向用户库存添加卡牌"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 生成事务ID
            transaction_id = str(uuid.uuid4())
            
            # 添加卡牌到库存
            inventory = await InventoryManager.add_cards_to_user(
                user_id, template_id, quantity, source, transaction_id
            )
            
            return ResponseFormatter.success({
                "template_id": template_id,
                "quantity": inventory.quantity,
                "total_acquired": inventory.total_acquired,
                "transaction_id": transaction_id
            }, f"成功添加 {quantity} 个 {template_id}")
            
        except Exception as e:
            logger.error(f"添加卡牌到库存失败: {e}")
            return ResponseFormatter.error(f"添加卡牌失败: {str(e)}")

    @staticmethod
    async def consume_cards_from_inventory(user_id: str, template_id: str, quantity: int, 
                                         reason: str = "game_action") -> Dict[str, Any]:
        """从用户库存消耗卡牌"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 检查库存是否充足
            inventory = await InventoryItem.get_by_user_and_template(user_id, template_id)
            if not inventory or inventory.quantity < quantity:
                return ResponseFormatter.error(
                    f"库存不足，当前数量: {inventory.quantity if inventory else 0}，需要: {quantity}",
                    code=400
                )
                
            # 生成事务ID
            transaction_id = str(uuid.uuid4())
            
            # 消耗卡牌
            success = await InventoryManager.consume_cards_from_user(
                user_id, template_id, quantity, reason, transaction_id
            )
            
            if success:
                # 重新获取库存信息
                updated_inventory = await InventoryItem.get_by_user_and_template(user_id, template_id)
                return ResponseFormatter.success({
                    "template_id": template_id,
                    "consumed_quantity": quantity,
                    "remaining_quantity": updated_inventory.quantity if updated_inventory else 0,
                    "transaction_id": transaction_id
                }, f"成功消耗 {quantity} 个 {template_id}")
            else:
                return ResponseFormatter.error("消耗失败", code=500)
                
        except Exception as e:
            logger.error(f"消耗卡牌失败: {e}")
            return ResponseFormatter.error(f"消耗卡牌失败: {str(e)}")

    @staticmethod
    async def get_inventory_transactions(user_id: str, template_id: str = None, 
                                       limit: int = 50) -> Dict[str, Any]:
        """获取用户库存交易历史"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 获取交易历史
            transactions = await InventoryTransaction.get_user_transactions(
                user_id, template_id, limit
            )
            
            transaction_list = [tx.dict() for tx in transactions]
            
            return ResponseFormatter.success({
                "transactions": transaction_list,
                "total": len(transaction_list)
            }, "获取交易历史成功")
            
        except Exception as e:
            logger.error(f"获取交易历史失败: {e}")
            return ResponseFormatter.error(f"获取交易历史失败: {str(e)}")

    @staticmethod
    async def grant_starter_cards(user_id: str) -> Dict[str, Any]:
        """为新用户发放启动卡包"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 检查是否已经发放过启动卡包
            existing_inventory = await InventoryItem.get_user_inventory(user_id)
            if existing_inventory:
                return ResponseFormatter.error("用户已拥有库存，无法重复发放启动卡包", code=400)
                
            # 发放启动卡包
            result = await InventoryManager.grant_starter_pack(user_id)
            
            if result["success"]:
                return ResponseFormatter.success(result, "启动卡包发放成功")
            else:
                return ResponseFormatter.error("启动卡包发放失败", code=500)
                
        except Exception as e:
            logger.error(f"发放启动卡包失败: {e}")
            return ResponseFormatter.error(f"发放启动卡包失败: {str(e)}")

    @staticmethod
    async def batch_update_inventory(user_id: str, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量更新库存"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            results = []
            transaction_id = str(uuid.uuid4())
            
            for update in updates:
                try:
                    operation = update.get("operation")
                    template_id = update.get("template_id")
                    quantity = update.get("quantity", 1)
                    
                    if operation == "add":
                        source = update.get("source", "batch_operation")
                        await InventoryManager.add_cards_to_user(
                            user_id, template_id, quantity, source, transaction_id
                        )
                        results.append({
                            "template_id": template_id,
                            "operation": "add",
                            "quantity": quantity,
                            "success": True
                        })
                    elif operation == "consume":
                        reason = update.get("reason", "batch_consumption")
                        success = await InventoryManager.consume_cards_from_user(
                            user_id, template_id, quantity, reason, transaction_id
                        )
                        results.append({
                            "template_id": template_id,
                            "operation": "consume",
                            "quantity": quantity,
                            "success": success
                        })
                    else:
                        results.append({
                            "template_id": template_id,
                            "operation": operation,
                            "success": False,
                            "error": "不支持的操作类型"
                        })
                        
                except Exception as e:
                    results.append({
                        "template_id": update.get("template_id"),
                        "operation": update.get("operation"),
                        "success": False,
                        "error": str(e)
                    })
            
            success_count = len([r for r in results if r.get("success")])
            
            return ResponseFormatter.success({
                "results": results,
                "success_count": success_count,
                "total_count": len(updates),
                "transaction_id": transaction_id
            }, f"批量操作完成，成功 {success_count}/{len(updates)} 项")
            
        except Exception as e:
            logger.error(f"批量更新库存失败: {e}")
            return ResponseFormatter.error(f"批量更新失败: {str(e)}")

    @staticmethod
    async def create_game_session(user_id: str, session_type: str, 
                                session_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """创建游戏会话"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 检查是否已有活跃会话
            existing_session = await GameSession.get_active_session(user_id, session_type)
            if existing_session:
                # 使旧会话过期
                await existing_session.expire_session()
                
            # 创建新会话
            session = GameSession(
                user_id=user_id,
                session_type=session_type,
                session_data=session_data or {},
                expires_at=datetime.utcnow() + timedelta(hours=2)  # 2小时后过期
            )
            await session.save()
            
            return ResponseFormatter.success({
                "session_id": str(session.id),
                "session_type": session_type,
                "session_data": session.session_data,
                "expires_at": session.expires_at.isoformat() if session.expires_at else None
            }, "游戏会话创建成功")
            
        except Exception as e:
            logger.error(f"创建游戏会话失败: {e}")
            return ResponseFormatter.error(f"创建游戏会话失败: {str(e)}")

    @staticmethod
    async def update_game_session(user_id: str, session_id: str, 
                                session_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新游戏会话数据"""
        try:
            # 验证用户存在
            user = await User.get_by_id(user_id)
            if not user:
                return ResponseFormatter.error("用户不存在", code=404)
                
            # 查找会话
            session = await GameSession.find_one({"id": session_id, "user_id": user_id, "status": "active"})
            if not session:
                return ResponseFormatter.error("会话不存在或已过期", code=404)
                
            # 更新会话数据
            await session.update_session_data(session_data)
            
            return ResponseFormatter.success({
                "session_id": session_id,
                "session_data": session.session_data,
                "updated_at": session.updated_at.isoformat()
            }, "会话数据更新成功")
            
        except Exception as e:
            logger.error(f"更新游戏会话失败: {e}")
            return ResponseFormatter.error(f"更新游戏会话失败: {str(e)}")

    @staticmethod
    async def check_card_availability(user_id: str, template_id: str, 
                                    required_quantity: int = 1) -> Dict[str, Any]:
        """检查卡牌可用性"""
        try:
            # 获取库存
            inventory = await InventoryItem.get_by_user_and_template(user_id, template_id)
            
            available = inventory.quantity if inventory else 0
            is_sufficient = available >= required_quantity
            
            return ResponseFormatter.success({
                "template_id": template_id,
                "available_quantity": available,
                "required_quantity": required_quantity,
                "is_sufficient": is_sufficient
            }, "检查完成")
            
        except Exception as e:
            logger.error(f"检查卡牌可用性失败: {e}")
            return ResponseFormatter.error(f"检查失败: {str(e)}") 