"""
库存管理API端点
提供用户库存的RESTful接口
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from pydantic import BaseModel, Field

from ....core.deps import get_current_user
from ....services.inventory_service import InventoryService
from ....utils.response_utils import ResponseFormatter

router = APIRouter()

# 请求模型
class AddCardsRequest(BaseModel):
    template_id: str = Field(..., description="策略模板ID")
    quantity: int = Field(..., ge=1, description="数量")
    source: str = Field(default="activity", description="来源")

class ConsumeCardsRequest(BaseModel):
    template_id: str = Field(..., description="策略模板ID")
    quantity: int = Field(..., ge=1, description="数量")
    reason: str = Field(default="strategy_execution", description="消耗原因")

class TransferCardsRequest(BaseModel):
    to_user_id: str = Field(..., description="目标用户ID")
    template_id: str = Field(..., description="策略模板ID")
    quantity: int = Field(..., ge=1, description="数量")

class CreateSessionRequest(BaseModel):
    session_type: str = Field(..., description="会话类型")
    session_data: Dict[str, Any] = Field(default_factory=dict, description="会话数据")

class UpdateSessionRequest(BaseModel):
    session_data: Dict[str, Any] = Field(..., description="会话数据")

class BatchUpdateRequest(BaseModel):
    updates: List[Dict[str, Any]] = Field(..., description="批量更新操作")

@router.get("/inventory")
async def get_inventory(
    available_only: bool = Query(False, description="仅显示可用物品"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取用户库存"""
    user_id = current_user["id"]
    result = await InventoryService.get_user_inventory(user_id, available_only)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "获取库存失败")
        )
    
    return result

@router.post("/inventory/add")
async def add_cards(
    request: AddCardsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """添加卡牌到库存"""
    user_id = current_user["id"]
    result = await InventoryService.add_cards_to_inventory(
        user_id, request.template_id, request.quantity, request.source
    )
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "添加卡牌失败")
        )
    
    return result

@router.post("/inventory/consume")
async def consume_cards(
    request: ConsumeCardsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """消耗库存卡牌"""
    user_id = current_user["id"]
    result = await InventoryService.consume_cards_from_inventory(
        user_id, request.template_id, request.quantity, request.reason
    )
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "消耗卡牌失败")
        )
    
    return result

@router.get("/inventory/transactions")
async def get_transaction_history(
    template_id: Optional[str] = Query(None, description="策略模板ID"),
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取交易历史"""
    user_id = current_user["id"]
    result = await InventoryService.get_transaction_history(user_id, template_id, limit)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "获取交易历史失败")
        )
    
    return result

@router.post("/inventory/transfer")
async def transfer_cards(
    request: TransferCardsRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """转移卡牌给其他用户"""
    user_id = current_user["id"]
    result = await InventoryService.transfer_cards_between_users(
        user_id, request.to_user_id, request.template_id, request.quantity
    )
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "转移卡牌失败")
        )
    
    return result

@router.post("/inventory/grant-starter")
async def grant_starter_cards(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """为新用户发放初始卡牌"""
    user_id = current_user["id"]
    result = await InventoryService.grant_starter_cards(user_id)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "发放初始卡牌失败")
        )
    
    return result

@router.post("/inventory/batch-update")
async def batch_update_inventory(
    request: BatchUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """批量更新库存"""
    user_id = current_user["id"]
    result = await InventoryService.batch_update_inventory(user_id, request.updates)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "批量更新失败")
        )
    
    return result

@router.post("/sessions")
async def create_game_session(
    request: CreateSessionRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """创建游戏会话"""
    user_id = current_user["id"]
    result = await InventoryService.create_game_session(
        user_id, request.session_type, request.session_data
    )
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "创建会话失败")
        )
    
    return result

@router.put("/sessions/{session_id}")
async def update_game_session(
    session_id: str,
    request: UpdateSessionRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """更新游戏会话"""
    result = await InventoryService.update_game_session(session_id, request.session_data)
    
    if not result.get("success"):
        raise HTTPException(
            status_code=result.get("code", 500),
            detail=result.get("message", "更新会话失败")
        )
    
    return result

@router.get("/sessions/active")
async def get_active_sessions(
    session_type: Optional[str] = Query(None, description="会话类型"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """获取用户活跃会话"""
    try:
        from ....models.inventory import GameSession
        
        user_id = current_user["id"]
        session = await GameSession.get_active_session(user_id, session_type)
        
        if not session:
            return ResponseFormatter.success(None, "无活跃会话")
            
        return ResponseFormatter.success({
            "session_id": session.id,
            "session_type": session.session_type,
            "session_data": session.session_data,
            "status": session.status,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "expires_at": session.expires_at.isoformat() if session.expires_at else None
        }, "获取活跃会话成功")
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取活跃会话失败: {str(e)}"
        ) 