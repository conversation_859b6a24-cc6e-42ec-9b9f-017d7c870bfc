from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from ..core.data.db.base import MongoModel

class StrategyCard(MongoModel):
    """策略卡片模型（策略模板）"""
    name: str = Field(..., description="策略卡片名称")
    description: str = Field(default="", description="策略卡片描述")
    type: str = Field(..., description="策略类型")
    category: str = Field(..., description="策略分类")
    icon: Optional[str] = Field(None, description="策略图标")
    parameters: List[Dict[str, Any]] = Field(default_factory=list, description="策略参数配置")
    template_code: str = Field(..., description="策略模板代码")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: Optional[str] = Field(None, description="创建者ID")
    is_public: bool = Field(default=True, description="是否公开")
    tags: List[str] = Field(default_factory=list, description="标签")
    version: str = Field(default="1.0.0", description="版本号")

    class Config:
        collection = "strategy_cards" 