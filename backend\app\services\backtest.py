from datetime import datetime
from typing import List, Dict, Any, Optional
import backtrader as bt
import pandas as pd
from ..models.backtest import BacktestRecord, BacktestConfig, BacktestResult
from ..models.strategy import StrategyGroup
from ..core.exceptions import BacktestError
from ..utils.id_utils import MongoIDHandler
from ..utils.error_utils import <PERSON>rrorHandler
from ..utils.response_utils import ResponseFormatter

class BacktestService:
    """回测服务"""
    
    @staticmethod
    async def create_backtest(data: Dict[str, Any]) -> Dict[str, Any]:
        """创建回测记录"""
        try:
            # 设置默认值
            data["created_at"] = datetime.utcnow()
            data["updated_at"] = data["created_at"]
            data["status"] = "pending"

            # 创建记录
            record = BacktestRecord(**data)
            await record.save()

            # 格式化响应
            return ResponseFormatter.format_document(record.to_dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="创建",
                entity="回测记录",
                details={"error": str(e)}
            )

    @staticmethod
    async def get_backtest(backtest_id: str) -> Dict[str, Any]:
        """获取回测记录"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(backtest_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("回测记录", backtest_id)

            # 查找记录
            record = await BacktestRecord.get(obj_id)
            if not record:
                ErrorHandler.raise_not_found("回测记录", backtest_id)

            # 格式化响应
            return ResponseFormatter.format_document(record.to_dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="获取",
                entity="回测记录",
                details={"error": str(e)}
            )

    @staticmethod
    async def list_backtests(
        user_id: str,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> tuple[List[Dict[str, Any]], int]:
        """获取回测记录列表"""
        try:
            # 构建查询条件
            query = {"user_id": user_id}
            if filters:
                query.update(filters)

            # 使用聚合管道优化查询
            pipeline = [
                {"$match": query},
                {"$sort": {"created_at": -1}},
                {"$skip": skip},
                {"$limit": limit},
                {
                    "$project": {
                        "_id": 1,
                        "name": 1,
                        "strategy_id": 1,
                        "status": 1,
                        "start_time": 1,
                        "end_time": 1,
                        "created_at": 1,
                        "updated_at": 1,
                        "metrics": 1
                    }
                }
            ]

            # 执行查询
            records = await BacktestRecord.aggregate(pipeline).to_list(length=None)
            total = await BacktestRecord.count_documents(query)

            # 格式化响应
            formatted_records = [ResponseFormatter.format_document(record) for record in records]
            return formatted_records, total

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="获取",
                entity="回测记录列表",
                details={"error": str(e)}
            )

    @staticmethod
    async def update_backtest(backtest_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新回测记录"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(backtest_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("回测记录", backtest_id)

            # 查找记录
            record = await BacktestRecord.get(obj_id)
            if not record:
                ErrorHandler.raise_not_found("回测记录", backtest_id)

            # 更新时间
            data["updated_at"] = datetime.utcnow()

            # 更新记录
            await record.update({"$set": data})

            # 格式化响应
            return ResponseFormatter.format_document(record.to_dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="更新",
                entity="回测记录",
                details={"error": str(e)}
            )

    @staticmethod
    async def delete_backtest(backtest_id: str) -> None:
        """删除回测记录"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(backtest_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("回测记录", backtest_id)

            # 查找记录
            record = await BacktestRecord.get(obj_id)
            if not record:
                ErrorHandler.raise_not_found("回测记录", backtest_id)

            # 删除记录
            await record.delete()

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="删除",
                entity="回测记录",
                details={"error": str(e)}
            )

    @staticmethod
    async def start_backtest(backtest_id: str) -> Dict[str, Any]:
        """启动回测"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(backtest_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("回测记录", backtest_id)

            # 查找记录
            record = await BacktestRecord.get(obj_id)
            if not record:
                ErrorHandler.raise_not_found("回测记录", backtest_id)

            # 检查状态
            if record.status == "running":
                ErrorHandler.raise_operation_failed(
                    operation="启动",
                    entity="回测",
                    details={"error": "回测已在运行中"}
                )

            # 更新状态
            await record.update({
                "$set": {
                    "status": "running",
                    "start_time": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            })

            # 格式化响应
            return ResponseFormatter.format_document(record.to_dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="启动",
                entity="回测",
                details={"error": str(e)}
            )

    @staticmethod
    async def stop_backtest(backtest_id: str) -> Dict[str, Any]:
        """停止回测"""
        try:
            # 验证并转换ID
            obj_id = MongoIDHandler.validate_and_convert(backtest_id)
            if not obj_id:
                ErrorHandler.raise_invalid_id("回测记录", backtest_id)

            # 查找记录
            record = await BacktestRecord.get(obj_id)
            if not record:
                ErrorHandler.raise_not_found("回测记录", backtest_id)

            # 检查状态
            if record.status != "running":
                ErrorHandler.raise_operation_failed(
                    operation="停止",
                    entity="回测",
                    details={"error": "回测未在运行中"}
                )

            # 更新状态
            await record.update({
                "$set": {
                    "status": "stopped",
                    "end_time": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
            })

            # 格式化响应
            return ResponseFormatter.format_document(record.to_dict())

        except Exception as e:
            ErrorHandler.raise_operation_failed(
                operation="停止",
                entity="回测",
                details={"error": str(e)}
            )

    @classmethod
    async def run_backtest(cls, backtest_id: str) -> Optional[BacktestRecord]:
        """执行回测"""
        record = await cls.get_backtest(backtest_id)
        if not record:
            return None

        try:
            # 更新状态为运行中
            await cls.update_backtest(backtest_id, {
                "status": "running",
                "progress": 0.0
            })

            # 获取策略组
            strategy_group = await StrategyGroup.get(record.strategy_group_id)
            if not strategy_group:
                raise BacktestError("策略组不存在")

            # 创建回测引擎
            cerebro = bt.Cerebro()
            
            # 设置回测参数
            cerebro.broker.setcash(record.config.initial_capital)
            cerebro.broker.setcommission(commission=record.config.transaction_fee)
            
            # 加载数据
            data = await cls._load_backtest_data(
                record.config.start_date,
                record.config.end_date
            )
            cerebro.adddata(data)
            
            # 添加策略
            for card in strategy_group.cards:
                strategy_class = cls._create_strategy_class(card)
                cerebro.addstrategy(strategy_class, **card.get("parameters", {}))
            
            # 添加分析器
            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')
            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
            cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trades')
            
            # 运行回测
            results = cerebro.run()
            
            # 处理回测结果
            result = cls._process_backtest_results(results[0])
            
            # 更新回测记录
            await cls.update_backtest(backtest_id, {
                "status": "completed",
                "progress": 100.0,
                "result": result,
                "completed_at": datetime.utcnow()
            })

            return record

        except Exception as e:
            # 更新错误状态
            await cls.update_backtest(backtest_id, {
                "status": "failed",
                "error_message": str(e)
            })
            raise BacktestError(f"回测执行失败: {str(e)}")

    @staticmethod
    async def _load_backtest_data(start_date: datetime, end_date: datetime) -> bt.feeds.PandasData:
        """加载回测数据"""
        # TODO: 实现数据加载逻辑
        pass

    @staticmethod
    def _create_strategy_class(strategy_card: Dict[str, Any]) -> type:
        """创建策略类"""
        # TODO: 实现策略类创建逻辑
        pass

    @staticmethod
    def _process_backtest_results(results) -> BacktestResult:
        """处理回测结果"""
        # 获取分析器结果
        sharpe = results.analyzers.sharpe.get_analysis()
        drawdown = results.analyzers.drawdown.get_analysis()
        trades = results.analyzers.trades.get_analysis()
        
        # 计算关键指标
        return BacktestResult(
            total_returns=results.broker.getvalue() / results.broker.startingcash - 1,
            annual_returns=sharpe.get('sharperatio', 0) * (252 ** 0.5),
            max_drawdown=drawdown.get('max', {}).get('drawdown', 0),
            sharpe_ratio=sharpe.get('sharperatio', 0),
            volatility=0.0,  # TODO: 计算波动率
            win_rate=trades.get('won', 0) / (trades.get('total', 1) or 1),
            profit_factor=abs(trades.get('pnl', {}).get('gross', {}).get('won', 0)) /
                        abs(trades.get('pnl', {}).get('gross', {}).get('lost', 1) or 1),
            total_trades=trades.get('total', 0),
            daily_stats={}  # TODO: 添加每日统计数据
        ) 