"""
价格过滤策略
"""
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType
from app.core.runtime.types import StrategyMode
from app.core.data.db.base import db_manager
from app.core.runtime.cache import cache_manager
from app.services.realtime_quote_service import RealTimeQuoteService
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class PriceFilterStrategy(UnifiedStrategyCard):
    """价格过滤策略"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化实时行情服务
        self.realtime_quote_service = RealTimeQuoteService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> pd.DataFrame:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return pd.DataFrame()
            
            # 获取模式配置 - 直接使用执行模式作为数据源配置
            mode = context.mode.value if context.mode else "filter"
            self._log(f"执行模式: {mode}")
            
            # 获取股票代码列表
            symbols = self._get_stock_symbols(context)
            
            # 择时模式: 使用实时行情数据
            if mode == "timing":
                return await self._get_timing_data(symbols)
            
            # 选股模式: 使用原有的数据获取逻辑
            mode_config = self.config["data_sources"].get(mode, self.config["data_sources"]["filter"])
            
            # 检查缓存
            cache_key = f"price_filter_{mode}_{','.join(sorted(symbols)) if symbols else 'all'}"
            cache = cache_manager["market_data"]
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                if symbols and not cached_data.empty:
                    # 使用索引过滤而不是创建新的DataFrame，提高性能
                    if '代码' in cached_data.columns:
                        symbol_mask = cached_data['代码'].isin(symbols)
                        filtered_cache = cached_data.loc[symbol_mask]
                        self._log(f"从缓存获取: {len(filtered_cache)}只股票")
                        return filtered_cache
                return cached_data
            
            # 获取数据
            db_type = mode_config["database"]["type"]
            if db_type == "postgresql":
                data = await self._get_data_from_postgresql(mode_config, symbols)
            else:
                self._log(f"不支持的数据库类型: {db_type}", "warning")
                return pd.DataFrame()
            
            if data.empty:
                return pd.DataFrame()
            
            # 缓存数据
            cache.set(cache_key, data, ttl=60)
            return data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_timing_data(self, symbols: List[str]) -> pd.DataFrame:
        """获取择时模式需要的实时数据"""
        try:
            if not symbols:
                self._log("未指定股票代码，请确保已设置择时标的", "warning")
                return pd.DataFrame()
            
            # 记录将要获取的股票代码
            self._log(f"准备获取以下标的的实时行情: {','.join(symbols)}")
            
            # 使用RealTimeQuoteService获取实时行情
            quotes_dict = await self.realtime_quote_service.get_realtime_quotes(symbols)
            
            if not quotes_dict:
                self._log("获取实时行情数据失败", "warning")
                return pd.DataFrame()
            
            # 提前预分配数组大小，避免动态增长
            rows = []
            rows_capacity = len(quotes_dict)
            rows.reserve(rows_capacity) if hasattr(rows, 'reserve') else None
            
            for symbol, quote_df in quotes_dict.items():
                if quote_df.empty:
                    continue
                
                # 提取最新行情，直接使用浮点型避免后续转换
                latest_quote = quote_df.iloc[0]
                
                rows.append({
                    "代码": symbol,
                    "名称": symbol,  # 可能需要从其他服务获取名称
                    "最新价": float(latest_quote.get("latest_price", 0))
                })
            
            # 一次性创建DataFrame
            result_df = pd.DataFrame(rows)
            
            # 指定数据类型，避免自动推断
            if not result_df.empty and '最新价' in result_df.columns:
                result_df['最新价'] = result_df['最新价'].astype('float64')
            
            self._log(f"成功获取 {len(result_df)} 只股票的实时行情数据")
            return result_df
            
        except Exception as e:
            self._log(f"获取择时数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    async def _get_data_from_postgresql(self, mode_config: Dict[str, Any], symbols: List[str]) -> pd.DataFrame:
        """从PostgreSQL获取数据"""
        try:
            if "database" not in mode_config or "table" not in mode_config["database"]:
                raise ValueError("配置错误：数据源未定义表名")
            
            table = mode_config["database"]["table"]
            fields = mode_config.get("fields", ["代码", "名称", "最新价"])
            
            fields_str = ", ".join(fields)
            
            # 使用参数化查询替代字符串拼接
            if symbols:
                async with db_manager.get_session() as session:
                    sql_query = f"SELECT {fields_str} FROM {table} WHERE 代码 = ANY(:symbols)"
                    result = await session.execute(text(sql_query), {'symbols': symbols})
                    rows = result.fetchall()
            else:
                async with db_manager.get_session() as session:
                    sql_query = f"SELECT {fields_str} FROM {table}"
                    result = await session.execute(text(sql_query))
                    rows = result.fetchall()
            
            # 创建DataFrame并直接指定数据类型
            df = pd.DataFrame(rows, columns=fields)
            
            # 确保最新价是浮点型
            if not df.empty and '最新价' in df.columns:
                df['最新价'] = df['最新价'].astype('float64')
                
            return df
                
        except Exception as e:
            self._log(f"从PostgreSQL获取数据失败: {str(e)}", "error")
            return pd.DataFrame()
            
    async def generate_signal(self, data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if data.empty or "最新价" not in data.columns:
                return []
            
            # 获取运行模式
            mode = self.context.mode.value if self.context and self.context.mode else "filter"
            
            # 获取参数，直接转换为浮点型
            value1 = float(params.get("price1") or params.get("value1", 0))
            value2 = float(params.get("price2") or params.get("value2", 0))
            operator = params.get("operator", "大于")
            
            self._log(f"价格筛选条件: {operator} {value1}" + (f" - {value2}" if operator == "区间" else ""))
            
            # 使用向量化操作进行筛选，避免循环
            if operator == "大于":
                filtered_data = data[data["最新价"] > value1]
            elif operator == "小于":
                filtered_data = data[data["最新价"] < value1]
            else:  # 区间
                filtered_data = data[(data["最新价"] >= value1) & (data["最新价"] <= value2)]
            
            self._log(f"筛选出 {len(filtered_data)}只股票")
            
            # 择时模式处理
            if mode == "timing":
                return self._generate_timing_signals(filtered_data, params)
            
            # 使用列表推导式而不是循环，更加高效
            signals = [
                self.create_signal(
                    symbol=row["代码"],
                    name=row["名称"] if "名称" in row and pd.notna(row["名称"]) else row["代码"],
                    direction="BUY" if operator == "大于" else "SELL",
                    signal_type="price",
                    confidence=0.8,
                    trigger_condition=f"价格{operator}_{value1}" + (f"-{value2}" if operator == "区间" else ""),
                    latest_price=row["最新价"]  # 这里已经是float类型，不需要再次转换
                )
                for _, row in filtered_data.iterrows()
            ]
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
            
    def _generate_timing_signals(self, filtered_data: pd.DataFrame, params: Dict[str, Any]) -> List[Signal]:
        """生成择时信号"""
        try:
            # 获取信号类型参数（买入/卖出/观望）
            signal_type = params.get("signal_type", "BUY")
            
            # 记录使用的信号类型
            if "signal_type" not in params:
                self._log(f"未设置信号类型，默认使用买入(BUY)信号", "warning")
            else:
                self._log(f"使用信号类型: {signal_type}")
                
            symbols = self._get_stock_symbols(self.context)
            
            # 如果没有符合条件的股票，生成观望信号
            if filtered_data.empty:
                if not symbols:
                    # 检查元数据中的timing_symbols
                    if self.context and self.context.metadata.get("timing_symbols"):
                        symbols_str = self.context.metadata["timing_symbols"]
                        if isinstance(symbols_str, str):
                            symbols = [s.strip() for s in symbols_str.split(",") if s.strip()]
                        else:
                            symbols = symbols_str
                        
                        if symbols:
                            self._log(f"从元数据获取了择时标的: {','.join(symbols)}")
                    
                    if not symbols:
                        self._log("未指定任何股票代码，无法生成观望信号", "warning")
                        return []
                
                # 为所有股票生成观望信号，使用列表推导式代替循环
                self._log(f"价格条件未触发，为 {len(symbols)} 只股票生成观望信号")
                return [
                    self.create_signal(
                        symbol=symbol,
                        name=symbol,
                        direction="HOLD",
                        signal_type="price",
                        confidence=0.5,
                        trigger_condition="价格条件未触发",
                        latest_price=0.0  # 直接使用浮点数
                    )
                    for symbol in symbols
                ]
            
            # 条件触发，生成指定类型的信号
            self._log(f"价格条件已触发，为 {len(filtered_data)} 只股票生成 {signal_type} 信号")
            return [
                self.create_signal(
                    symbol=row["代码"],
                    name=row["名称"] if "名称" in row and pd.notna(row["名称"]) else row["代码"],
                    direction=signal_type,
                    signal_type="price",
                    confidence=0.9,
                    trigger_condition="价格条件已触发",
                    latest_price=row["最新价"]  # 这里已经是float类型，不需要再次转换
                )
                for _, row in filtered_data.iterrows()
            ]
            
        except Exception as e:
            self._log(f"生成择时信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 保存上下文
            self.context = context
            
            # 添加执行模式日志
            mode_name = "择时" if context.mode.value == "timing" else "选股"
            self._log(f"执行策略模式: {context.mode} ({mode_name})")
            
            # 检查标的设置
            if context.mode.value == "timing":
                # 检查元数据中的timing_symbols
                symbols = []
                if context.metadata.get("timing_symbols"):
                    symbols_str = context.metadata["timing_symbols"]
                    if isinstance(symbols_str, str):
                        symbols = [s.strip() for s in symbols_str.split(",") if s.strip()]
                    else:
                        symbols = symbols_str
                    
                    if symbols:
                        self._log(f"本次执行将使用择时标的: {','.join(symbols)}")
                    else:
                        self._log("警告: 虽然设置了择时模式，但未指定有效的择时标的", "warning")
                else:
                    self._log("警告: 未指定择时标的", "warning")
                
                # 在择时模式下，确保有signal_type参数
                if "signal_type" not in context.parameters:
                    self._log("警告: 未设置信号类型参数，将默认使用'BUY'", "warning")
                    # 默认设置为买入信号
                    context.parameters["signal_type"] = "BUY"
            
            # 检查是否在串行执行中接收前一个策略的信号
            self._check_sequential_mode(context)
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 检查数据
            if data is None or data.empty:
                self._log("没有获取到有效数据，无法继续执行", "warning")
                return []
                
            # 记录数据信息
            self._log(f"成功获取数据: {len(data)}行")
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            signal_type = "观望" if all(signal.direction == "HOLD" for signal in signals) else "交易"
            self._log(f"策略执行完成, 生成{len(signals)}个{signal_type}信号")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []