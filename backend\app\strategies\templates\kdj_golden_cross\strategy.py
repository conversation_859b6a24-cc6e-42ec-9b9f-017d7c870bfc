"""
KDJ黄金交叉择时策略
基于KDJ指标的黄金交叉和死叉进行择时交易
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class KDJGoldenCross(UnifiedStrategyCard):
    """KDJ黄金交叉择时策略：使用KDJ指标的交叉信号进行择时"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 获取策略参数
            params = context.parameters
            k_period = params.get("k_period", 9)
            d_period = params.get("d_period", 3)
            j_period = params.get("j_period", 3)
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 计算KDJ指标
                df = self._calculate_kdj(df, k_period, d_period, j_period)
                
                # 识别交叉信号
                df = self._identify_cross_signals(df, params)
                
                # 打印最近的KDJ数据用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条KDJ数据:")
                    recent_data = df.tail(last_rows).copy()
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    debug_cols = ['formatted_time', 'close', 'k_value', 'd_value', 'j_value', 'golden_cross', 'death_cross']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的KDJ数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_kdj(self, df: pd.DataFrame, k_period: int, d_period: int, j_period: int) -> pd.DataFrame:
        """计算KDJ指标"""
        try:
            # 计算最高价和最低价的移动窗口
            df['lowest_low'] = df['low'].rolling(window=k_period).min()
            df['highest_high'] = df['high'].rolling(window=k_period).max()
            
            # 计算RSV (未成熟随机值)
            df['rsv'] = ((df['close'] - df['lowest_low']) / 
                        (df['highest_high'] - df['lowest_low']) * 100).fillna(50)
            
            # 计算K值 (快速随机指标)
            df['k_value'] = df['rsv'].ewm(alpha=1/d_period).mean()
            
            # 计算D值 (慢速随机指标)
            df['d_value'] = df['k_value'].ewm(alpha=1/d_period).mean()
            
            # 计算J值 (加速随机指标)
            df['j_value'] = j_period * df['k_value'] - (j_period - 1) * df['d_value']
            
            # 限制J值范围
            df['j_value'] = np.clip(df['j_value'], -20, 120)
            
            return df
            
        except Exception as e:
            self._log(f"计算KDJ失败: {str(e)}", "error")
            return df
    
    def _identify_cross_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """识别KDJ交叉信号"""
        try:
            oversold_level = float(params.get("oversold_level", 20))
            overbought_level = float(params.get("overbought_level", 80))
            j_acceleration_threshold = float(params.get("j_acceleration_threshold", 10))
            
            df['golden_cross'] = False
            df['death_cross'] = False
            df['j_acceleration'] = False
            df['cross_strength'] = 0.0
            
            for i in range(1, len(df)):
                if pd.isna(df.iloc[i]['k_value']) or pd.isna(df.iloc[i-1]['k_value']):
                    continue
                
                current_k = df.iloc[i]['k_value']
                current_d = df.iloc[i]['d_value']
                current_j = df.iloc[i]['j_value']
                prev_k = df.iloc[i-1]['k_value']
                prev_d = df.iloc[i-1]['d_value']
                prev_j = df.iloc[i-1]['j_value']
                
                # 黄金交叉：K线上穿D线
                if prev_k <= prev_d and current_k > current_d:
                    # 在超卖区域的黄金交叉更有效
                    if current_k <= oversold_level or current_d <= oversold_level:
                        df.iloc[i, df.columns.get_loc('golden_cross')] = True
                        
                        # 计算交叉强度
                        cross_gap = current_k - current_d
                        position_bonus = max(0, oversold_level - min(current_k, current_d)) / oversold_level
                        strength = cross_gap + position_bonus * 10
                        df.iloc[i, df.columns.get_loc('cross_strength')] = strength
                
                # 死叉：K线下穿D线
                elif prev_k >= prev_d and current_k < current_d:
                    # 在超买区域的死叉更有效
                    if current_k >= overbought_level or current_d >= overbought_level:
                        df.iloc[i, df.columns.get_loc('death_cross')] = True
                        
                        # 计算交叉强度
                        cross_gap = current_d - current_k
                        position_bonus = max(0, min(current_k, current_d) - overbought_level) / (100 - overbought_level)
                        strength = cross_gap + position_bonus * 10
                        df.iloc[i, df.columns.get_loc('cross_strength')] = strength
                
                # J值加速信号
                if pd.notna(prev_j) and pd.notna(current_j):
                    j_change = current_j - prev_j
                    if abs(j_change) >= j_acceleration_threshold:
                        df.iloc[i, df.columns.get_loc('j_acceleration')] = True
            
            return df
            
        except Exception as e:
            self._log(f"识别交叉信号失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            # 获取参数
            oversold_level = float(params.get("oversold_level", 20))
            overbought_level = float(params.get("overbought_level", 80))
            min_cross_strength = float(params.get("min_cross_strength", 2.0))
            use_j_confirmation = params.get("use_j_confirmation", True)
            
            self._log(f"KDJ策略参数: 超卖线={oversold_level}, 超买线={overbought_level}, "
                     f"最小交叉强度={min_cross_strength}, J值确认={use_j_confirmation}")
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < 20:
                    self._log(f"股票{symbol}数据不足，无法生成信号")
                    continue
                
                # 获取最近的交叉信号
                recent_golden = df[df['golden_cross'] == True].tail(3)
                recent_death = df[df['death_cross'] == True].tail(3)
                
                # 获取当前KDJ状态
                latest = df.iloc[-1]
                current_k = latest['k_value']
                current_d = latest['d_value']
                current_j = latest['j_value']
                current_price = latest['close']
                
                # 黄金交叉买入信号
                for _, row in recent_golden.iterrows():
                    cross_strength = row['cross_strength']
                    if cross_strength >= min_cross_strength:
                        
                        # J值确认
                        j_confirmed = True
                        if use_j_confirmation:
                            j_confirmed = row['j_value'] > row['k_value']
                        
                        if j_confirmed:
                            confidence = min(0.9, 0.5 + cross_strength / 20 + 
                                           (row.get('j_acceleration', False) * 0.1))
                            
                            signals.append(
                                self.create_signal(
                                    symbol=symbol,
                                    name=symbol,
                                    direction="BUY",
                                    signal_type="technical",
                                    confidence=confidence,
                                    trigger_condition=f"KDJ黄金交叉_K{row['k_value']:.1f}_D{row['d_value']:.1f}",
                                    current_price=float(current_price),
                                    k_value=float(row['k_value']),
                                    d_value=float(row['d_value']),
                                    j_value=float(row['j_value']),
                                    cross_strength=float(cross_strength)
                                )
                            )
                
                # 死叉卖出信号
                for _, row in recent_death.iterrows():
                    cross_strength = row['cross_strength']
                    if cross_strength >= min_cross_strength:
                        
                        # J值确认
                        j_confirmed = True
                        if use_j_confirmation:
                            j_confirmed = row['j_value'] < row['k_value']
                        
                        if j_confirmed:
                            confidence = min(0.9, 0.5 + cross_strength / 20 + 
                                           (row.get('j_acceleration', False) * 0.1))
                            
                            signals.append(
                                self.create_signal(
                                    symbol=symbol,
                                    name=symbol,
                                    direction="SELL",
                                    signal_type="technical",
                                    confidence=confidence,
                                    trigger_condition=f"KDJ死叉_K{row['k_value']:.1f}_D{row['d_value']:.1f}",
                                    current_price=float(current_price),
                                    k_value=float(row['k_value']),
                                    d_value=float(row['d_value']),
                                    j_value=float(row['j_value']),
                                    cross_strength=float(cross_strength)
                                )
                            )
                
                # 如果没有明确的交叉信号，生成状态信号
                if len([s for s in signals if s.symbol == symbol]) == 0:
                    if pd.notna(current_k) and pd.notna(current_d):
                        if current_k <= oversold_level and current_d <= oversold_level:
                            condition = f"KDJ超卖区域_K{current_k:.1f}_D{current_d:.1f}"
                            direction = "HOLD"
                            confidence = 0.4
                        elif current_k >= overbought_level and current_d >= overbought_level:
                            condition = f"KDJ超买区域_K{current_k:.1f}_D{current_d:.1f}"
                            direction = "HOLD"
                            confidence = 0.4
                        else:
                            condition = f"KDJ中性区域_K{current_k:.1f}_D{current_d:.1f}"
                            direction = "HOLD"
                            confidence = 0.3
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=direction,
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                k_value=float(current_k),
                                d_value=float(current_d),
                                j_value=float(current_j) if pd.notna(current_j) else 0
                            )
                        )
            
            self._log(f"共生成{len(signals)}个KDJ信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行KDJ黄金交叉策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"KDJ策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []