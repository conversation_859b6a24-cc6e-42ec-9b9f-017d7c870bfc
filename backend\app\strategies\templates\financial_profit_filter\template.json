{"template_id": "financial_profit_filter", "name": "财务盈利", "description": "根据最新一季度的公司财务指标筛选股票", "version": "1.0.0", "author": "QuantCard", "stars": 2, "tags": ["基本面", "选股", "财务"], "parameters": {"financial_indicator": {"type": "select", "label": "财务指标", "description": "选择财务指标类型", "required": true, "default": "营业收入", "options": [{"label": "营业收入", "value": "营业收入"}, {"label": "净利润", "value": "净利润"}, {"label": "毛利率", "value": "毛利率"}]}, "indicator_operator": {"type": "select", "label": "条件", "description": "财务指标筛选条件", "required": true, "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}]}, "indicator_value": {"type": "number", "label": "数值", "description": "财务指标值", "required": false, "default": null, "validation": {"min": 0, "max": 1000000}, "unit_map": {"营业收入": "万元", "净利润": "万元", "毛利率": "%"}}, "yoy_change_operator": {"type": "select", "label": "同比变动条件", "description": "同比变动筛选条件", "required": true, "default": "大于", "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}], "visibleWhen": {"parameter": "financial_indicator", "value": ["营业收入", "净利润"]}}, "yoy_change_value": {"type": "number", "label": "同比变动值", "description": "同比变动值", "required": false, "default": null, "validation": {"min": -100, "max": 1000}, "unit": "%", "visibleWhen": {"parameter": "financial_indicator", "value": ["营业收入", "净利润"]}}}, "parameterGroups": {"indicator_filter": {"parameters": ["financial_indicator"], "displayMode": "inline", "prefix": "财务指标", "separator": "-", "layout": "horizontal"}, "value_filter": {"parameters": ["indicator_operator", "indicator_value"], "displayMode": "inline", "prefix": "数值条件", "separator": "-", "layout": "horizontal"}, "yoy_change_filter": {"parameters": ["yoy_change_operator", "yoy_change_value"], "displayMode": "inline", "prefix": "同比变动", "separator": "-", "layout": "horizontal", "visibleWhen": {"parameter": "financial_indicator", "value": ["营业收入", "净利润"]}}}, "ui": {"icon": "AccountBookOutlined", "color": "#1890FF", "group": "基本面", "order": 4, "form": {"layout": "vertical", "labelWidth": "100px", "wrapperWidth": "300px"}}, "template_code": "financial_profit_filter/strategy.py"}