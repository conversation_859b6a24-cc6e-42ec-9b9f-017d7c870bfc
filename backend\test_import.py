#!/usr/bin/env python3
"""
测试导入脚本 - 验证所有模块是否能正常导入
"""

def test_imports():
    """测试关键模块的导入"""
    try:
        print("测试导入 app.main...")
        import app.main
        print("✅ app.main 导入成功")
        
        print("测试导入 unified_websocket...")
        from app.api.unified_websocket import websocket_manager, UnifiedWebSocketHandler
        print("✅ unified_websocket 导入成功")
        
        print("测试导入 websocket_endpoint...")
        from app.api.websocket_endpoint import ws_router
        print("✅ websocket_endpoint 导入成功")
        
        print("测试导入 in_memory_queue...")
        from app.services.in_memory_queue import in_memory_queue
        print("✅ in_memory_queue 导入成功")
        
        # 测试 handle_message 方法是否存在
        if hasattr(websocket_manager, 'handle_message'):
            print("✅ websocket_manager.handle_message 方法存在")
        else:
            print("❌ websocket_manager.handle_message 方法不存在")
            return False
            
        print("\n🎉 所有导入测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    exit(0 if success else 1)
