name: 布林带突破策略
description: 基于布林带的高级突破策略，包含挤压形态识别和假突破过滤
version: 1.0.0
author: QuantCard团队
tags:
  - 技术分析
  - 布林带
  - 突破策略
  - 挤压形态
  - 波动率

strategy_class_name: BollingerBreakoutStrategy

parameters:
  bb_period:
    type: int
    default: 20
    min: 10
    max: 50
    description: 布林带计算周期
  bb_std:
    type: float
    default: 2.0
    min: 1.0
    max: 3.0
    description: 布林带标准差倍数
  adaptive_std:
    type: boolean
    default: false
    description: 是否启用自适应标准差调整
  squeeze_threshold:
    type: float
    default: 0.1
    min: 0.05
    max: 0.5
    description: 挤压识别阈值
  min_squeeze_periods:
    type: int
    default: 5
    min: 3
    max: 15
    description: 最小挤压持续周期
  min_volume_ratio:
    type: float
    default: 1.5
    min: 1.0
    max: 3.0
    description: 突破确认的最小成交量倍数
  breakout_confirmation:
    type: int
    default: 2
    min: 1
    max: 5
    description: 突破信号确认周期数
  use_squeeze_filter:
    type: boolean
    default: true
    description: 是否启用挤压后突破优先
  rsi_oversold:
    type: float
    default: 30.0
    min: 20.0
    max: 40.0
    description: RSI超卖阈值
  rsi_overbought:
    type: float
    default: 70.0
    min: 60.0
    max: 80.0
    description: RSI超买阈值

data_sources:
  timing:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 150
    
  backtest:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 500
    
  monitor:
    database:
      type: questdb
      table: kline_1min
    fields:
      - symbol
      - time
      - open
      - high
      - low
      - close
      - volume
      - amount
    time_field: time
    frequency: 1m
    window_size: 150

outputs:
  timing:
    type: signal
    fields:
      - symbol
      - name
      - direction
      - confidence
      - trigger_condition
      - current_price
      - bb_upper
      - bb_mid
      - bb_lower
      - bb_position
      - bb_width
      - breakout_type
      - stop_loss
      - volume_ratio
      - rsi
      - is_squeeze
      - post_squeeze_breakout
      
  backtest:
    type: backtest_result
    fields:
      - symbol
      - name
      - entry_time
      - entry_price
      - exit_time
      - exit_price
      - return_pct
      - breakout_type
      - squeeze_before_entry
      
  monitor:
    type: alert
    fields:
      - symbol
      - name
      - alert_type
      - trigger_condition
      - bb_position
      - squeeze_status
      - alert_time