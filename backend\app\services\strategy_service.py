"""
策略服务类
负责处理策略相关的业务逻辑，包括策略组、策略卡片、策略模板等
"""
from typing import Dict, Any, List, Optional, Tuple, TYPE_CHECKING
from datetime import datetime
from bson import ObjectId
import logging

# 使用 TYPE_CHECKING 条件导入，这样在运行时不会导入，只在类型检查时导入
if TYPE_CHECKING:
    from ..core.runtime import StrategyRuntime

# 实际导入需要在运行时使用的类型
from ..core.runtime.types import StrategyMode, RuntimeContext, ExecutionResult, Signal

from ..utils.response_utils import ResponseFormatter
from ..utils.error_utils import ErrorHandler
from ..core.data.db.base import db_manager

logger = logging.getLogger(__name__)

class StrategyService:
    """策略服务类"""
    
    def __init__(self, runtime = None):
        """初始化策略服务"""
        self.runtime = runtime
        
    async def list_strategies(
        self, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 10,
        name: Optional[str] = None,
        tag: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户的策略列表"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 构建查询条件
            query = {"user_id": user_id}
            if name:
                query["name"] = {"$regex": name, "$options": "i"}
            if tag:
                query["tags"] = tag
                
            # 获取总数
            total = await db.strategies.count_documents(query)
            
            # 获取策略列表
            cursor = db.strategies.find(query).sort("created_at", -1).skip(skip).limit(limit)
            strategies = await cursor.to_list(length=limit)
            
            # 处理ObjectId
            for strategy in strategies:
                strategy["id"] = str(strategy["_id"])
                del strategy["_id"]
                
            return strategies, total
            
        except Exception as e:
            logger.error(f"获取策略列表失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略列表", {"error": str(e)})
    
    async def get_strategy_template(self, id: str) -> Dict[str, Any]:
        """获取策略模板详情"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            template = None
            
            # 首先尝试通过ObjectId查询
            if ObjectId.is_valid(id):
                try:
                    template = await db.strategy_templates.find_one({"_id": ObjectId(id)})
                except Exception:
                    logger.warning(f"通过ObjectId查询模板失败: {id}")
            
            # 如果通过ObjectId没找到，尝试通过template_id字段查询
            if not template:
                logger.info(f"通过template_id字段查询模板: {id}")
                template = await db.strategy_templates.find_one({"template_id": id})
                
            # 如果仍然找不到，尝试其他可能的字段
            if not template:
                # 尝试通过id字段查询（字符串形式的ID）
                template = await db.strategy_templates.find_one({"id": id})
                
            if not template:
                ErrorHandler.raise_not_found("策略模板", id)
                
            # 处理ObjectId
            if "_id" in template:
                template["id"] = str(template["_id"])
                del template["_id"]
            
            return template
            
        except Exception as e:
            logger.error(f"获取策略模板详情失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略模板", {"error": str(e)})
            
    async def list_strategy_templates(
        self,
        skip: int = 0,
        limit: int = 10,
        strategy_type: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取策略模板列表
        
        Args:
            skip: 跳过的数量
            limit: 返回的数量上限
            strategy_type: 已弃用，使用 tags 替代
            tags: 标签过滤，支持多标签，包括功能标签（"选股"/"择时"/"回测"）
            
        Returns:
            Tuple[List[Dict[str, Any]], int]: 策略模板列表和总数
        """
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 构建查询条件
            query = {}
                
            # 如果提供了 strategy_type，转换为对应的 tag 查询
            # 这是为了兼容旧的 API 调用，新代码应直接使用 tags
            if strategy_type:
                tag_to_find = None
                if strategy_type == "filter":
                    tag_to_find = "选股"
                elif strategy_type == "timing":
                    tag_to_find = "择时"
                    
                if tag_to_find:
                    if tags:
                        tags.append(tag_to_find)
                    else:
                        tags = [tag_to_find]
            
            if tags and len(tags) > 0:
                # 使用$in操作符支持多标签查询
                query["tags"] = {"$in": tags}
                
            # 获取总数
            total = await db.strategy_templates.count_documents(query)
            
            # 查询模板
            cursor = db.strategy_templates.find(query).sort("created_at", -1).skip(skip).limit(limit)
            templates = await cursor.to_list(length=limit)
            
            # 处理ObjectId
            for template in templates:
                if "_id" in template:
                    template["id"] = str(template["_id"])
                    del template["_id"]
            
            return templates, total
            
        except Exception as e:
            logger.error(f"获取策略模板列表失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略模板列表", {"error": str(e)})
    
    async def list_strategy_groups(
        self,
        user_id: str,
        skip: int = 0,
        limit: int = 10
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取用户的策略组列表"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 获取总数
            total = await db.strategy_groups.count_documents({"user_id": user_id})
            
            # 获取策略组列表
            cursor = db.strategy_groups.find({"user_id": user_id}).skip(skip).limit(limit)
            groups = await cursor.to_list(length=limit)
            
            # 处理ObjectId和确保必要字段存在
            for group in groups:
                group["id"] = str(group["_id"])
                del group["_id"]
                
                # 确保必要字段存在
                if "status" not in group:
                    group["status"] = "inactive"
                if "execution_mode" not in group:
                    group["execution_mode"] = "sequential"
                if "performance_metrics" not in group:
                    group["performance_metrics"] = {}
                if "description" not in group:
                    group["description"] = ""
                
                # 确保created_by字段存在（兼容旧数据）
                if "created_by" not in group and "user_id" in group:
                    group["created_by"] = group["user_id"]
            
            return groups, total
            
        except Exception as e:
            logger.error(f"获取策略组列表失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略组列表", {"error": str(e)})
    
    async def get_strategy_group(self, group_id: str, user_id: str) -> Dict[str, Any]:
        """获取策略组详情"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
                
            # 处理ObjectId
            group["id"] = str(group["_id"])
            del group["_id"]
            
            # 收集模板ID
            template_ids = []
            cards = group.get("cards", [])
            
            # 从所有卡片中提取模板ID
            for card in cards:
                if "template_id" in card and card["template_id"]:
                    template_ids.append(card["template_id"])
            
            # 获取模板信息
            templates = []
            if template_ids:
                # 去重
                unique_template_ids = list(set(template_ids))
                
                # 查询模板
                template_cursor = db.strategy_templates.find(
                    {"_id": {"$in": [ObjectId(tid) for tid in unique_template_ids if ObjectId.is_valid(tid)]}}
                )
                
                templates_data = await template_cursor.to_list(length=len(unique_template_ids))
                
                # 处理模板数据
                for template in templates_data:
                    if "_id" in template:
                        template["id"] = str(template["_id"])
                        del template["_id"]
                    templates.append(template)
            
            # 将模板信息添加到响应中，但不嵌入到策略卡实例中
            return {
                "group": group,
                "templates": templates
            }
            
        except Exception as e:
            logger.error(f"获取策略组详情失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略组", {"error": str(e)})
            
    async def create_strategy_group(self, data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """创建策略组"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 创建时间
            now = datetime.utcnow()
            
            # 处理cards参数
            cards = []
            for card in data.get("cards", []):
                # 只处理包含template_id的卡片
                if "template_id" in card:
                    card_copy = {
                        "id": card.get("id"),
                        "name": card.get("name", ""),
                        "description": card.get("description", ""),
                        "parameters": card.get("parameters", {}),
                        "template_id": card["template_id"],
                        "template_name": card.get("template_name", ""),
                        "position": card.get("position", {"x": 0, "y": 0})
                    }
                    cards.append(card_copy)
                
            # 构建文档
            document = {
                "name": data.get("name", "未命名策略组"),
                "description": data.get("description", ""),
                "user_id": user_id,
                "created_by": user_id,
                "cards": cards,
                "group_type": data.get("group_type", "filter"),
                "execution_mode": data.get("execution_mode", "sequential"),
                "timing_symbols": data.get("timing_symbols", ""),
                "kline_period": data.get("kline_period", "5min"),
                "execution_config": data.get("execution_config", {}),
                "status": "inactive",
                "is_active": True,
                "created_at": now,
                "updated_at": now,
                "execution_logs": [],
                "performance_metrics": {}
            }
            
            # 插入文档
            result = await db.strategy_groups.insert_one(document)
            
            # 获取插入的文档
            created_group = await db.strategy_groups.find_one({"_id": result.inserted_id})
            
            # 处理返回对象
            created_group["id"] = str(created_group["_id"])
            del created_group["_id"]
            
            return created_group
            
        except Exception as e:
            logger.error(f"创建策略组失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("创建", "策略组", {"error": str(e)})
            
    async def update_strategy_group(self, group_id: str, data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """更新策略组"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
            
            # 处理cards参数 - 只使用引用结构
            if "cards" in data:
                cards = []
                for card in data["cards"]:
                    # 只处理包含template_id的卡片
                    if "template_id" in card:
                        card_copy = {
                            "id": card.get("id"),
                            "name": card.get("name", ""),
                            "description": card.get("description", ""),
                            "parameters": card.get("parameters", {}),
                            "template_id": card["template_id"],
                            "template_name": card.get("template_name", ""),
                            "position": card.get("position", {"x": 0, "y": 0})
                        }
                        cards.append(card_copy)
            
            # 更新data中的cards
            data["cards"] = cards
            
            # 更新时间
            data["updated_at"] = datetime.utcnow()
            
            # 执行更新
            await db.strategy_groups.update_one(
                {"_id": ObjectId(group_id)},
                {"$set": data}
            )
            
            # 获取更新后的数据
            updated_group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            updated_group["id"] = str(updated_group["_id"])
            del updated_group["_id"]
            
            return updated_group
            
        except Exception as e:
            logger.error(f"更新策略组失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("更新", "策略组", {"error": str(e)})
            
    async def delete_strategy_group(self, group_id: str, user_id: str) -> Dict[str, Any]:
        """删除策略组"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("删除", "策略组", {"error": "无权删除"})
                
            # 删除策略组
            result = await db.strategy_groups.delete_one({"_id": ObjectId(group_id)})
            
            if result.deleted_count == 0:
                ErrorHandler.raise_operation_failed("删除", "策略组", {"error": "删除失败"})
                
            return {"message": "策略组已成功删除"}
            
        except Exception as e:
            logger.error(f"删除策略组失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("删除", "策略组", {"error": str(e)})
            
    async def get_strategy_tags(self) -> List[Dict[str, Any]]:
        """获取策略标签列表"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 从数据库查询标签
            tags_cursor = db.strategy_tags.find({})
            tags = await tags_cursor.to_list(length=100)
            
            # 处理ObjectId
            for tag in tags:
                if "_id" in tag:
                    tag["id"] = str(tag["_id"])
                    del tag["_id"]
            
            # 如果没有标签数据，提供一些默认标签
            if not tags:
                default_tags = [
                    {"name": "高收益", "color": "#f50"},
                    {"name": "低风险", "color": "#87d068"},
                    {"name": "技术指标", "color": "#108ee9"},
                    {"name": "基本面", "color": "#2db7f5"}
                ]
                # 将默认标签保存到数据库
                await db.strategy_tags.insert_many(default_tags)
                tags = default_tags
                
            return tags
            
        except Exception as e:
            logger.error(f"获取策略标签失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("获取", "策略标签", {"error": str(e)})
            
    async def update_strategy_tag(self, tag_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新策略标签"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询标签是否存在
            existing_tag = await db.strategy_tags.find_one({"name": tag_name})
            
            # 添加时间戳
            data["updated_at"] = datetime.utcnow()
            
            if existing_tag:
                # 更新标签
                result = await db.strategy_tags.update_one(
                    {"name": tag_name},
                    {"$set": data}
                )
                
                if result.modified_count == 0:
                    logger.warning(f"标签 {tag_name} 更新未生效")
            else:
                # 创建新标签
                data["name"] = tag_name
                data["created_at"] = datetime.utcnow()
                await db.strategy_tags.insert_one(data)
            
            # 获取更新后的标签
            updated_tag = await db.strategy_tags.find_one({"name": tag_name})
            
            if updated_tag and "_id" in updated_tag:
                updated_tag["id"] = str(updated_tag["_id"])
                del updated_tag["_id"]
                
            return updated_tag
            
        except Exception as e:
            logger.error(f"更新标签失败: {str(e)}", exc_info=True)
            ErrorHandler.raise_operation_failed("更新", "策略标签", {"error": str(e)})
            
    async def execute_strategy(self, request_data: Dict[str, Any], user_id: str, debug: bool = True) -> Dict[str, Any]:
        """执行策略并返回结果 - 作为策略组执行"""
        try:
            if not self.runtime:
                ErrorHandler.raise_operation_failed("执行", "策略", {"error": "运行时环境未初始化"})
                
            start_time = datetime.utcnow().timestamp()
            logger.info(f"开始执行策略: ID={request_data['id']}, 调试={debug}")
            
            # 获取第一个模板
            first_template = await self.get_strategy_template(request_data["id"])
            
            # 创建上下文列表，首先添加第一个策略卡的上下文
            contexts = []
            
            # 获取策略组类型和择时标的
            group_type = request_data.get("group_type", "filter")
            timing_symbols = request_data.get("timing_symbols")
            
            # 根据策略组类型确定策略模式
            strategy_mode = StrategyMode.TIMING if group_type == "timing" else StrategyMode.FILTER
            
            # 第一个策略卡上下文
            first_ctx = RuntimeContext(
                strategy_id=request_data["id"],
                user_id=user_id,
                name=first_template.get("name", "未命名策略"),
                type=first_template.get("type", "unknown"),
                description=first_template.get("description", ""),
                parameters=request_data.get("parameters") or {},
                mode=strategy_mode,
                debug=debug
            )
            
            # 添加择时标的到上下文元数据
            if group_type == "timing" and timing_symbols:
                first_ctx.metadata["timing_symbols"] = timing_symbols
                logger.info(f"设置择时标的: {timing_symbols}")
                
                # 获取并设置K线周期到组级别元数据
                kline_period = request_data.get("kline_period")
                if kline_period:
                    first_ctx.metadata["group_kline_period"] = kline_period
                    logger.info(f"设置策略组K线周期: {kline_period}")
            
            contexts.append(first_ctx)
            
            # 添加后续策略卡上下文
            next_cards = request_data.get("next_cards") or []
            
            for card in next_cards:
                card_id = card.get("id")
                if not card_id:
                    continue
                    
                template = await self.get_strategy_template(card_id)
                
                ctx = RuntimeContext(
                    strategy_id=card_id,
                    user_id=user_id,
                    name=template.get("name", ""),
                    type=card.get("template_type", "custom"),
                    description=template.get("description", ""),
                    parameters=card.get("parameters") or {},
                    mode=strategy_mode,
                    debug=debug
                )
                
                # 对后续卡片也设置择时标的
                if group_type == "timing" and timing_symbols:
                    ctx.metadata["timing_symbols"] = timing_symbols
                    
                    # 对后续卡片也设置相同的K线周期到元数据
                    kline_period = request_data.get("kline_period")
                    if kline_period:
                        ctx.metadata["group_kline_period"] = kline_period
                
                contexts.append(ctx)
            
            # 确定执行模式
            execution_mode = request_data.get("execution_mode", "sequential").lower()
            if execution_mode not in ["sequential", "parallel"]:
                execution_mode = "sequential"
            
            logger.info(f"执行策略组: 模式={execution_mode}, 数量={len(contexts)}")
            
            # 使用策略组执行逻辑
            return await self.runtime.execute_group(contexts, mode=execution_mode, group_type=group_type)
            
        except Exception as e:
            error_message = f"执行策略失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            return {
                "success": False,
                "message": error_message,
                "logs": [error_message],
                "data": None
            }

    async def execute_strategy_group(self, group_id: str, user_id: str, mode: str = "sequential") -> Dict[str, Any]:
        """执行策略组"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
            
            # 获取策略卡片列表
            cards = group.get("cards", [])
            logger.info(f"策略组 {group_id} 包含 {len(cards)} 个策略卡片")
            
            if not cards:
                ErrorHandler.raise_operation_failed("执行", "策略组", {"error": "策略组中没有策略卡片"})
            
            # 确定组类型 (择时/选股)
            group_type = group.get("group_type", "filter")
            logger.info(f"策略组类型: {group_type}")
            
            # 创建执行请求参数，与debug接口保持一致
            if len(cards) == 0:
                return {
                    "success": False,
                    "message": "策略组中没有有效的策略卡片",
                    "logs": ["策略组中没有策略卡片，无法执行"],
                    "data": {
                        "signals": [],
                        "statistics": {"total": 0},
                        "execution_time": datetime.utcnow().strftime("%Y/%m/%d %H:%M:%S")
                    }
                }
            
            # 提取第一个卡片作为主卡片
            main_card = cards[0]
            
            # 创建执行上下文
            contexts = []
            for i, card in enumerate(cards):
                # 获取模板ID
                template_id = card.get("template_id")
                if not template_id:
                    continue
                    
                # 获取策略模板信息
                template = await self.get_strategy_template(template_id)
                if not template:
                    logger.warning(f"策略模板不存在: {template_id}")
                    continue
                
                # 获取参数
                parameters = card.get("parameters", {})
                
                # 创建元数据，包含策略组信息
                metadata = {
                    "strategy_group": {
                        "id": str(group["_id"]),
                        "name": group.get("name", ""),
                        "type": group_type
                    },
                    "group_kline_period": group.get("kline_period") if group_type == "timing" else None,
                    "timing_symbols": group.get("timing_symbols") if group_type == "timing" else None
                }
                
                # 创建上下文
                ctx = RuntimeContext(
                    strategy_id=template_id,
                    user_id=user_id,
                    name=card.get("name", ""),
                    type=template.get("type", "custom"),  # 从模板获取类型
                    description=card.get("description", ""),
                    parameters=parameters,
                    mode=StrategyMode.FILTER,  # 默认为过滤模式，会在execute_group中根据组类型修改
                    debug=False,  # 生产环境执行
                    metadata=metadata
                )
                
                contexts.append(ctx)
            
            # 使用本地时间作为执行开始时间
            execution_start_time = datetime.now()
            
            # 调用策略组执行器
            result = await self.runtime.execute_group(
                contexts=contexts,
                mode=mode,
                group_type=group_type
            )
            
            # 记录执行结果到数据库
            execution_time = (datetime.now() - execution_start_time).total_seconds()
            
            # 使用新方法保存执行历史
            status = "completed" if result.get("success", False) else "error"
            await self.save_execution_history(group_id, user_id, result, execution_time, status, group_type)
            
            return result
            
        except Exception as e:
            error_message = f"执行策略组失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            
            # 即使出错也尝试记录执行历史
            try:
                await self.save_execution_history(
                    group_id, 
                    user_id, 
                    {
                        "success": False,
                        "message": error_message,
                        "logs": [error_message],
                        "data": None
                    }, 
                    0.0, 
                    "error",
                    "unknown"
                )
            except Exception as save_error:
                logger.error(f"保存执行历史失败: {save_error}")
            
            return {
                "success": False,
                "message": error_message,
                "logs": [error_message],
                "data": None
            }

    async def save_execution_history(self, group_id: str, user_id: str, result: Dict, 
                                    execution_time: float, status: str, group_type: str) -> bool:
        """
        保存策略执行历史，并限制为每个策略组保留最近10条记录
        
        Args:
            group_id: 策略组ID
            user_id: 用户ID
            result: 执行结果
            execution_time: 执行时间(秒)
            status: 执行状态
            group_type: 策略组类型
            
        Returns:
            bool: 保存是否成功
        """
        try:
            from app.models.strategy import StrategyExecutionHistory
            
            # 根据策略类型精简要保存的结果数据
            result_to_save = {
                "success": result.get("success", False),
                "message": result.get("message", ""),
            }
            
            # 保存数据部分
            if "data" in result and isinstance(result["data"], dict):
                result_to_save["data"] = {
                    "execution_time": result["data"].get("execution_time", ""),
                    "statistics": result["data"].get("statistics", {}),
                }
                
                # 只保存信号数据，不保存详细日志
                if "signals" in result["data"]:
                    result_to_save["data"]["signals"] = result["data"]["signals"]
            
            # 处理日志信息:
            # 1. 错误结果保留所有日志
            # 2. 择时策略只保留错误日志
            # 3. 选股策略保留完整日志但最多100条
            logs = result.get("logs", [])
            if logs:
                if not result.get("success", False):
                    # 失败情况保留所有日志
                    result_to_save["logs"] = logs
                elif group_type == "timing":
                    # 择时策略只保留错误日志
                    error_logs = [log for log in logs if any(kw in log.lower() for kw in ["错误", "失败", "异常", "error", "fail", "exception"])]
                    if error_logs:
                        result_to_save["logs"] = error_logs
                else:
                    # 选股策略保留完整日志但最多100条
                    result_to_save["logs"] = logs[:100] if len(logs) > 100 else logs
            
            # 创建执行历史记录
            history = StrategyExecutionHistory(
                strategy_id=group_id,
                user_id=user_id,
                status=status,
                result=result_to_save,
                execution_time=execution_time * 1000,  # 转换为毫秒
                execution_duration=execution_time * 1000,  # 添加执行耗时字段，转换为毫秒
                error=result.get("message") if not result.get("success", False) else None,
                group_type=group_type
            )
            
            # 保存记录
            await history.insert()
            
            # 异步删除旧记录，只保留最近10条
            db = await db_manager.get_mongodb_database("quantcard")
            # 先查找需要删除的记录ID
            cursor = db.strategy_execution_history.find(
                {"strategy_id": group_id, "user_id": user_id}
            ).sort("created_at", -1).skip(10)
            
            # 收集旧记录ID
            old_ids = []
            async for doc in cursor:
                old_ids.append(doc["_id"])
            
            # 如果有旧记录，删除它们
            if old_ids:
                await db.strategy_execution_history.delete_many({"_id": {"$in": old_ids}})
                logger.debug(f"已删除 {len(old_ids)} 条旧执行历史记录")
                
            return True
        except Exception as e:
            logger.error(f"保存执行历史失败: {e}", exc_info=True)
            return False

    async def start_strategy_group(self, group_id: str, user_id: str) -> Dict[str, Any]:
        """启动策略组持续执行"""
        from ..services.scheduler_service import get_strategy_scheduler
        
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
            
            # 获取策略卡片列表
            cards = group.get("cards", [])
            logger.info(f"策略组 {group_id} 包含 {len(cards)} 个策略卡片")
            
            if not cards:
                ErrorHandler.raise_operation_failed("启动", "策略组", {"error": "策略组中没有策略卡片"})
            
            # 确定组类型 (择时/选股)
            group_type = group.get("group_type", "filter")
            logger.info(f"策略组类型: {group_type}")
            
            # 检查策略组类型，仅择时策略支持持续执行
            if group_type != "timing":
                return {
                    "success": False,
                    "message": "只有择时策略支持持续执行",
                    "task_id": None,
                    "status": "inactive"
                }
            
            # 获取K线周期，默认5分钟
            kline_period = group.get("kline_period", "5min")
            # 转换为调度器支持的周期格式
            scheduler_period = kline_period if kline_period in ["1min", "5min", "15min", "30min", "60min", "1h", "1d"] else "5min"
            
            # 获取策略调度器
            scheduler = await get_strategy_scheduler()
            
            # 检查是否已经在运行
            tasks = await scheduler.get_all_tasks()
            for task in tasks:
                if task.get("strategy_id") == group_id and task.get("active"):
                    # 更新状态
                    await db.strategy_groups.update_one(
                        {"_id": ObjectId(group_id)},
                        {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
                    )
                    
                    return {
                        "success": True,
                        "message": "策略组已在运行中",
                        "task_id": task.get("id"),
                        "status": "active"
                    }
            
            # 创建回调函数
            async def strategy_callback(strategy_id, parameters, metadata, user_id):
                """策略执行回调函数"""
                try:
                    # 执行策略组
                    await self.execute_strategy_group(group_id=strategy_id, user_id=user_id)
                    logger.info(f"定时执行策略组成功: {strategy_id}")
                except Exception as e:
                    logger.error(f"定时执行策略组失败: {strategy_id}, 错误: {str(e)}")
            
            # 调度策略执行
            task_id = await scheduler.schedule_strategy(
                strategy_id=group_id,
                period=scheduler_period,
                callback=strategy_callback,
                parameters={},
                metadata={"group_type": group_type, "kline_period": kline_period},
                user_id=user_id
            )
            
            # 更新策略组状态
            await db.strategy_groups.update_one(
                {"_id": ObjectId(group_id)},
                {"$set": {"status": "active", "updated_at": datetime.utcnow()}}
            )
            
            return {
                "success": True,
                "message": f"策略组已启动持续执行，周期: {kline_period}",
                "task_id": task_id,
                "status": "active"
            }
            
        except Exception as e:
            error_message = f"启动策略组失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            return {
                "success": False,
                "message": error_message,
                "task_id": None,
                "status": "error"
            }

    async def stop_strategy_group(self, group_id: str, user_id: str) -> Dict[str, Any]:
        """停止策略组持续执行"""
        from ..services.scheduler_service import get_strategy_scheduler
        
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
            
            # 获取策略调度器
            scheduler = await get_strategy_scheduler()
            
            # 查找并停止该策略组的所有任务
            tasks = await scheduler.get_all_tasks()
            stopped_tasks = []
            
            for task in tasks:
                if task.get("strategy_id") == group_id:
                    if await scheduler.unschedule_strategy(task.get("id")):
                        stopped_tasks.append(task.get("id"))
            
            # 更新策略组状态
            await db.strategy_groups.update_one(
                {"_id": ObjectId(group_id)},
                {"$set": {"status": "inactive", "updated_at": datetime.utcnow()}}
            )
            
            # 如果是择时策略，清理数据缓存
            if group.get("group_type") == "timing":
                # 获取K线周期
                kline_period = group.get("kline_period", "5min")
                # 生成缓存ID
                cache_group_id = f"{group_id}_{kline_period}"
                # 释放缓存
                self.runtime.data_manager.release_group_data(cache_group_id)
                logger.info(f"已释放策略组缓存: {cache_group_id}")
            
            return {
                "success": True,
                "message": f"策略组已停止执行，停止了{len(stopped_tasks)}个任务",
                "tasks_stopped": stopped_tasks,
                "status": "inactive"
            }
            
        except Exception as e:
            error_message = f"停止策略组失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            return {
                "success": False,
                "message": error_message,
                "tasks_stopped": [],
                "status": "error"
            }

    async def get_strategy_group_execution_history(self, group_id: str, user_id: str) -> List[Dict[str, Any]]:
        """获取策略组执行历史"""
        try:
            # 获取数据库连接
            db = await db_manager.get_mongodb_database("quantcard")
            
            # 查询策略组
            try:
                group = await db.strategy_groups.find_one({"_id": ObjectId(group_id)})
            except Exception:
                ErrorHandler.raise_invalid_id("策略组", group_id)
                
            if not group:
                ErrorHandler.raise_not_found("策略组", group_id)
                
            # 检查权限
            if group["user_id"] != user_id:
                ErrorHandler.raise_operation_failed("访问", "策略组", {"error": "无权访问"})
            
            # 查询执行历史
            history_cursor = db.strategy_execution_history.find({
                "strategy_id": group_id,
                "user_id": user_id
            }).sort("created_at", -1).limit(10)  # 最近10条记录
            
            history_list = []
            async for record in history_cursor:
                # 处理ObjectId和日期格式
                record["id"] = str(record["_id"])
                del record["_id"]
                
                if isinstance(record.get("created_at"), datetime):
                    record["execution_time"] = record["created_at"].isoformat()
                
                # 提取信号数据
                signals = []
                if record.get("result") and record["result"].get("data") and record["result"]["data"].get("signals"):
                    signals = record["result"]["data"]["signals"]
                
                # 添加到结果列表
                history_list.append({
                    "id": record["id"],
                    "execution_time": record.get("execution_time", ""),
                    "status": record.get("status", "unknown"),
                    "signals": signals,
                    "signals_count": len(signals),
                    "execution_duration": record.get("execution_duration", 0) # 修正为正确的字段名
                })
            
            return history_list
            
        except Exception as e:
            error_message = f"获取策略组执行历史失败: {str(e)}"
            logger.error(error_message, exc_info=True)
            return [] 