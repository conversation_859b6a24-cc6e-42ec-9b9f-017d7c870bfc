"""
自适应均线择时策略
基于多重指数平滑均线(EMA/DEMA/TEMA)的高级择时策略，具有自适应周期调整能力
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path
import talib as ta

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

class AdaptiveMATimingStrategy(UnifiedStrategyCard):
    """自适应均线择时策略
    
    该策略特点：
    1. 支持多种均线类型：SMA、EMA、DEMA、TEMA
    2. 自适应周期调整：根据市场波动率动态调整均线周期
    3. 多时间框架分析：结合不同周期的均线信号
    4. 趋势强度评估：使用ADX指标评估趋势强度
    5. 假突破过滤：通过量能和持续性确认信号
    6. 动态止损：基于ATR的动态止损位
    """
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                try:
                    processed_df = await self._process_kline_data(df, context.parameters)
                    if not processed_df.empty:
                        processed_data[symbol] = processed_df
                        self._log(f"成功处理股票 {symbol} 的K线数据，共{len(processed_df)}条记录")
                    else:
                        self._log(f"股票 {symbol} 数据处理后为空", "warning")
                except Exception as e:
                    self._log(f"处理股票 {symbol} 数据失败: {str(e)}", "error")
            
            return processed_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _process_kline_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """处理K线数据，计算各种技术指标"""
        try:
            if df.empty:
                return pd.DataFrame()
            
            # 确保数据按时间排序
            df = df.sort_values('time').copy()
            
            # 获取参数
            ma_type = params.get("ma_type", "EMA")
            base_period = int(params.get("base_period", 20))
            adaptive_enabled = params.get("adaptive_enabled", True)
            volatility_lookback = int(params.get("volatility_lookback", 20))
            
            # 计算波动率（用于自适应调整）
            df['returns'] = df['close'].pct_change()
            df['volatility'] = df['returns'].rolling(window=volatility_lookback).std()
            
            # 自适应周期计算
            if adaptive_enabled:
                df['adaptive_period'] = self._calculate_adaptive_period(
                    df['volatility'], base_period
                )
            else:
                df['adaptive_period'] = base_period
            
            # 计算多种均线
            df = self._calculate_moving_averages(df, ma_type, base_period)
            
            # 计算趋势强度指标 (ADX)
            df = self._calculate_adx(df)
            
            # 计算ATR（用于止损）
            df['atr'] = ta.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=14)
            
            # 计算成交量指标
            df = self._calculate_volume_indicators(df)
            
            # 生成交易信号
            df = self._generate_trading_signals(df, params)
            
            return df
            
        except Exception as e:
            self._log(f"处理K线数据失败: {str(e)}", "error")
            return pd.DataFrame()
    
    def _calculate_adaptive_period(self, volatility: pd.Series, base_period: int) -> pd.Series:
        """计算自适应周期"""
        try:
            # 计算波动率的分位数
            vol_median = volatility.median()
            vol_std = volatility.std()
            
            # 自适应调整逻辑：高波动时用短周期，低波动时用长周期
            adaptive_period = base_period + ((volatility - vol_median) / vol_std * -5).fillna(0)
            
            # 限制周期范围
            adaptive_period = np.clip(adaptive_period, base_period * 0.5, base_period * 1.5)
            
            return adaptive_period.round().astype(int)
            
        except Exception as e:
            self._log(f"计算自适应周期失败: {str(e)}", "error")
            return pd.Series([base_period] * len(volatility))
    
    def _calculate_moving_averages(self, df: pd.DataFrame, ma_type: str, period: int) -> pd.DataFrame:
        """计算各种类型的移动平均线"""
        try:
            close_values = df['close'].values
            
            if ma_type == "SMA":
                df['ma_fast'] = ta.SMA(close_values, timeperiod=period)
                df['ma_slow'] = ta.SMA(close_values, timeperiod=period * 2)
                
            elif ma_type == "EMA":
                df['ma_fast'] = ta.EMA(close_values, timeperiod=period)
                df['ma_slow'] = ta.EMA(close_values, timeperiod=period * 2)
                
            elif ma_type == "DEMA":
                df['ma_fast'] = ta.DEMA(close_values, timeperiod=period)
                df['ma_slow'] = ta.DEMA(close_values, timeperiod=period * 2)
                
            elif ma_type == "TEMA":
                df['ma_fast'] = ta.TEMA(close_values, timeperiod=period)
                df['ma_slow'] = ta.TEMA(close_values, timeperiod=period * 2)
            
            # 计算均线差值和斜率
            df['ma_diff'] = df['ma_fast'] - df['ma_slow']
            df['ma_diff_pct'] = (df['ma_diff'] / df['ma_slow'] * 100)
            
            # 计算快线斜率（趋势方向）
            df['ma_fast_slope'] = df['ma_fast'].diff(3) / 3  # 3期斜率
            df['ma_slow_slope'] = df['ma_slow'].diff(5) / 5  # 5期斜率
            
            return df
            
        except Exception as e:
            self._log(f"计算移动平均线失败: {str(e)}", "error")
            return df
    
    def _calculate_adx(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算ADX趋势强度指标"""
        try:
            high_values = df['high'].values
            low_values = df['low'].values
            close_values = df['close'].values
            
            # 计算ADX指标
            df['adx'] = ta.ADX(high_values, low_values, close_values, timeperiod=14)
            df['plus_di'] = ta.PLUS_DI(high_values, low_values, close_values, timeperiod=14)
            df['minus_di'] = ta.MINUS_DI(high_values, low_values, close_values, timeperiod=14)
            
            # 趋势强度分类
            df['trend_strength'] = pd.cut(
                df['adx'], 
                bins=[0, 20, 40, 60, 100],
                labels=['弱趋势', '中等趋势', '强趋势', '极强趋势']
            )
            
            return df
            
        except Exception as e:
            self._log(f"计算ADX指标失败: {str(e)}", "error")
            return df
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算成交量指标"""
        try:
            # 成交量均线
            df['volume_ma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            # OBV指标
            df['obv'] = ta.OBV(df['close'].values, df['volume'].values)
            df['obv_ma'] = df['obv'].rolling(window=10).mean()
            
            return df
            
        except Exception as e:
            self._log(f"计算成交量指标失败: {str(e)}", "error")
            return df
    
    def _generate_trading_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成交易信号"""
        try:
            # 获取参数
            min_adx = float(params.get("min_adx", 20))
            min_volume_ratio = float(params.get("min_volume_ratio", 1.2))
            confirmation_periods = int(params.get("confirmation_periods", 2))
            
            # 初始化信号列
            df['signal'] = 'HOLD'
            df['signal_strength'] = 0.0
            
            for i in range(confirmation_periods, len(df)):
                current = df.iloc[i]
                previous = df.iloc[i-1]
                prev_prev = df.iloc[i-confirmation_periods]
                
                # 基础条件检查
                if pd.isna(current['ma_fast']) or pd.isna(current['ma_slow']):
                    continue
                
                # 均线交叉信号
                is_golden_cross = (previous['ma_fast'] <= previous['ma_slow'] and 
                                 current['ma_fast'] > current['ma_slow'])
                is_death_cross = (previous['ma_fast'] >= previous['ma_slow'] and 
                                current['ma_fast'] < current['ma_slow'])
                
                # 趋势确认
                trend_confirmed = current['adx'] >= min_adx
                
                # 成交量确认
                volume_confirmed = current['volume_ratio'] >= min_volume_ratio
                
                # 动量确认
                momentum_up = (current['ma_fast_slope'] > 0 and 
                             current['ma_slow_slope'] > 0)
                momentum_down = (current['ma_fast_slope'] < 0 and 
                               current['ma_slow_slope'] < 0)
                
                # 生成买入信号
                if (is_golden_cross and trend_confirmed and 
                    volume_confirmed and momentum_up):
                    signal_strength = min(0.9, 
                        0.3 + (current['adx'] - min_adx) / 100 + 
                        (current['volume_ratio'] - min_volume_ratio) * 0.1
                    )
                    df.loc[df.index[i], 'signal'] = 'BUY'
                    df.loc[df.index[i], 'signal_strength'] = signal_strength
                
                # 生成卖出信号
                elif (is_death_cross and trend_confirmed and 
                      volume_confirmed and momentum_down):
                    signal_strength = min(0.9,
                        0.3 + (current['adx'] - min_adx) / 100 + 
                        (current['volume_ratio'] - min_volume_ratio) * 0.1
                    )
                    df.loc[df.index[i], 'signal'] = 'SELL'
                    df.loc[df.index[i], 'signal_strength'] = signal_strength
            
            # 计算止损位
            df['stop_loss_long'] = df['close'] - df['atr'] * 2
            df['stop_loss_short'] = df['close'] + df['atr'] * 2
            
            return df
            
        except Exception as e:
            self._log(f"生成交易信号失败: {str(e)}", "error")
            return df

    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取最新信号
                latest_signals = df[df['signal'] != 'HOLD'].tail(5)  # 最近5个交易信号
                
                if latest_signals.empty:
                    # 生成观望信号
                    latest = df.iloc[-1]
                    signals.append(
                        self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction="HOLD",
                            signal_type="technical",
                            confidence=0.5,
                            trigger_condition="均线无明确信号",
                            current_price=float(latest['close']),
                            ma_fast=float(latest['ma_fast']) if pd.notna(latest['ma_fast']) else 0,
                            ma_slow=float(latest['ma_slow']) if pd.notna(latest['ma_slow']) else 0,
                            adx=float(latest['adx']) if pd.notna(latest['adx']) else 0,
                            trend_strength=str(latest['trend_strength']) if pd.notna(latest['trend_strength']) else "弱趋势"
                        )
                    )
                else:
                    # 处理交易信号
                    for _, row in latest_signals.iterrows():
                        direction = row['signal']
                        confidence = row['signal_strength']
                        
                        # 构建触发条件描述
                        ma_type = params.get("ma_type", "EMA")
                        base_period = params.get("base_period", 20)
                        
                        if direction == 'BUY':
                            condition = f"{ma_type}金叉_{base_period}周期"
                        else:
                            condition = f"{ma_type}死叉_{base_period}周期"
                        
                        # 计算止损位
                        if direction == 'BUY':
                            stop_loss = float(row['stop_loss_long'])
                        else:
                            stop_loss = float(row['stop_loss_short'])
                        
                        signal = self.create_signal(
                            symbol=symbol,
                            name=symbol,
                            direction=direction,
                            signal_type="technical",
                            confidence=confidence,
                            trigger_condition=condition,
                            current_price=float(row['close']),
                            ma_fast=float(row['ma_fast']) if pd.notna(row['ma_fast']) else 0,
                            ma_slow=float(row['ma_slow']) if pd.notna(row['ma_slow']) else 0,
                            adx=float(row['adx']) if pd.notna(row['adx']) else 0,
                            trend_strength=str(row['trend_strength']) if pd.notna(row['trend_strength']) else "弱趋势",
                            stop_loss=stop_loss,
                            atr=float(row['atr']) if pd.notna(row['atr']) else 0,
                            volume_ratio=float(row['volume_ratio']) if pd.notna(row['volume_ratio']) else 0
                        )
                        signals.append(signal)
            
            self._log(f"自适应均线策略生成{len(signals)}个信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []

    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            self._log("开始执行自适应均线择时策略")
            
            # 获取参数信息
            ma_type = context.parameters.get("ma_type", "EMA")
            base_period = context.parameters.get("base_period", 20)
            adaptive_enabled = context.parameters.get("adaptive_enabled", True)
            
            adaptive_text = "启用" if adaptive_enabled else "禁用"
            self._log(f"策略参数: 均线类型={ma_type}, 基础周期={base_period}, 自适应={adaptive_text}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            if not data:
                self._log("未获取到有效数据", "warning")
                return []
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录执行结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []