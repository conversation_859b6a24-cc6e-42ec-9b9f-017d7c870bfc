from typing import Any, Dict, List, Optional, Union, TypeVar
from datetime import datetime
from bson import ObjectId

T = TypeVar('T')

class ResponseFormatter:
    """响应格式转换工具类
    
    提供统一的响应格式化方法,支持:
    - 基础数据类型转换(datetime, ObjectId等)
    - 成功响应格式化
    """
    
    @staticmethod
    def format_datetime(dt: Optional[datetime]) -> Optional[str]:
        """格式化日期时间为ISO格式字符串"""
        return dt.isoformat() if dt else None

    @staticmethod
    def format_object_id(obj_id: Optional[ObjectId]) -> Optional[str]:
        """格式化MongoDB ObjectId为字符串"""
        return str(obj_id) if obj_id else None

    @staticmethod
    def format_document(doc: Dict[str, Any]) -> Dict[str, Any]:
        """格式化MongoDB文档,递归处理所有字段
        
        Args:
            doc: MongoDB文档字典
            
        Returns:
            格式化后的文档字典
        """
        formatted = {}
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                formatted[key] = ResponseFormatter.format_object_id(value)
            elif isinstance(value, datetime):
                formatted[key] = ResponseFormatter.format_datetime(value)
            elif isinstance(value, dict):
                formatted[key] = ResponseFormatter.format_document(value)
            elif isinstance(value, list):
                formatted[key] = [
                    ResponseFormatter.format_document(item) if isinstance(item, dict)
                    else ResponseFormatter.format_value(item)
                    for item in value
                ]
            else:
                formatted[key] = value
        return formatted

    @staticmethod
    def format_value(value: T) -> T:
        """格式化任意类型的值
        
        Args:
            value: 需要格式化的值
            
        Returns:
            格式化后的值,保持原类型
        """
        if isinstance(value, ObjectId):
            return ResponseFormatter.format_object_id(value)  # type: ignore
        elif isinstance(value, datetime):
            return ResponseFormatter.format_datetime(value)  # type: ignore
        elif isinstance(value, dict):
            return ResponseFormatter.format_document(value)  # type: ignore
        elif isinstance(value, list):
            return [ResponseFormatter.format_value(item) for item in value]  # type: ignore
        return value
        
    @staticmethod
    def success(
        data: Optional[T] = None,
        message: str = "操作成功",
        total: Optional[int] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """返回成功响应
        
        Args:
            data: 响应数据
            message: 成功消息
            total: 总数(用于分页)
            **kwargs: 其他自定义字段
            
        Returns:
            标准格式的成功响应
        """
        formatted_data = ResponseFormatter.format_value(data)
        
        # 如果数据已经是标准响应格式,直接返回
        if isinstance(formatted_data, dict) and "success" in formatted_data:
            return formatted_data
            
        response = {
            "success": True,
            "message": message,
            "data": formatted_data
        }
        
        if total is not None:
            response["total"] = total
            
        response.update(kwargs)
        return response 