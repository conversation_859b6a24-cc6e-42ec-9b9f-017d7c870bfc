"""
成交量价格确认择时策略
基于量价关系的择时策略，通过成交量确认价格趋势的可靠性
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class VolumePriceTiming(UnifiedStrategyCard):
    """成交量价格确认择时策略：基于量价配合关系的择时分析"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 初始化分钟K线服务
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            # 从context.data_cache获取K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取K线数据", "warning")
                return {}
            
            # 获取策略参数
            params = context.parameters
            volume_ma_period = params.get("volume_ma_period", 20)
            price_ma_period = params.get("price_ma_period", 10)
            
            # 处理每只股票的数据
            for symbol, df in kline_data.items():
                # 确保数据按时间排序
                df.sort_values(by='time', inplace=True)
                
                # 计算量价指标
                df = self._calculate_volume_price_indicators(df, volume_ma_period, price_ma_period)
                
                # 分析量价关系
                df = self._analyze_volume_price_relationship(df, params)
                
                # 生成量价信号
                df = self._generate_volume_price_signals(df, params)
                
                # 打印最近的量价数据用于调试
                last_rows = min(10, len(df))
                if last_rows > 0:
                    self._log(f"股票{symbol}最近{last_rows}条量价数据:")
                    recent_data = df.tail(last_rows).copy()
                    recent_data['formatted_time'] = recent_data['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
                    debug_cols = ['formatted_time', 'close', 'volume', 'volume_ratio', 'price_change_pct', 'vp_signal']
                    available_cols = [col for col in debug_cols if col in recent_data.columns]
                    self._log(recent_data[available_cols].to_string())
            
            self._log(f"成功获取{len(kline_data)}只股票的量价数据")
            return kline_data
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_volume_price_indicators(self, df: pd.DataFrame, volume_ma_period: int, price_ma_period: int) -> pd.DataFrame:
        """计算量价相关指标"""
        try:
            # 价格变化
            df['price_change'] = df['close'].diff()
            df['price_change_pct'] = df['close'].pct_change() * 100
            
            # 成交量指标
            df['volume_ma'] = df['volume'].rolling(window=volume_ma_period).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            df['volume_std'] = df['volume'].rolling(window=volume_ma_period).std()
            
            # 价格趋势
            df['price_ma'] = df['close'].rolling(window=price_ma_period).mean()
            df['price_trend'] = np.where(df['close'] > df['price_ma'], 'up', 
                                        np.where(df['close'] < df['price_ma'], 'down', 'flat'))
            
            # 成交额
            df['turnover'] = df['close'] * df['volume']
            df['turnover_ma'] = df['turnover'].rolling(window=volume_ma_period).mean()
            df['turnover_ratio'] = df['turnover'] / df['turnover_ma']
            
            # OBV (能量潮)
            df['obv'] = (df['volume'] * np.sign(df['price_change'])).cumsum()
            df['obv_ma'] = df['obv'].rolling(window=volume_ma_period).mean()
            df['obv_trend'] = np.where(df['obv'] > df['obv_ma'], 'up', 'down')
            
            return df
            
        except Exception as e:
            self._log(f"计算量价指标失败: {str(e)}", "error")
            return df
    
    def _analyze_volume_price_relationship(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """分析量价关系"""
        try:
            # 获取参数
            volume_surge_threshold = float(params.get("volume_surge_threshold", 2.0))
            price_change_threshold = float(params.get("price_change_threshold", 1.0))
            
            # 量价配合类型
            df['vp_type'] = 'normal'
            
            for i in range(len(df)):
                volume_ratio = df.iloc[i]['volume_ratio']
                price_change_pct = abs(df.iloc[i]['price_change_pct'])
                price_change = df.iloc[i]['price_change_pct']
                
                if pd.isna(volume_ratio) or pd.isna(price_change_pct):
                    continue
                
                # 放量上涨
                if (volume_ratio >= volume_surge_threshold and 
                    price_change >= price_change_threshold):
                    df.iloc[i, df.columns.get_loc('vp_type')] = 'surge_up'
                
                # 放量下跌
                elif (volume_ratio >= volume_surge_threshold and 
                      price_change <= -price_change_threshold):
                    df.iloc[i, df.columns.get_loc('vp_type')] = 'surge_down'
                
                # 缩量上涨
                elif (volume_ratio < 1.0 and 
                      price_change >= price_change_threshold):
                    df.iloc[i, df.columns.get_loc('vp_type')] = 'low_vol_up'
                
                # 缩量下跌
                elif (volume_ratio < 1.0 and 
                      price_change <= -price_change_threshold):
                    df.iloc[i, df.columns.get_loc('vp_type')] = 'low_vol_down'
                
                # 放量滞涨/滞跌
                elif volume_ratio >= volume_surge_threshold and price_change_pct < price_change_threshold:
                    df.iloc[i, df.columns.get_loc('vp_type')] = 'high_vol_stagnant'
            
            # 量价背离分析
            df['vp_divergence'] = False
            
            # 检测量价背离
            for i in range(5, len(df)):
                # 价格创新高但成交量萎缩
                recent_prices = df.iloc[i-4:i+1]['close']
                recent_volumes = df.iloc[i-4:i+1]['volume']
                
                if (df.iloc[i]['close'] == recent_prices.max() and 
                    df.iloc[i]['volume'] < recent_volumes.mean() * 0.8):
                    df.iloc[i, df.columns.get_loc('vp_divergence')] = True
                
                # 价格创新低但成交量萎缩
                elif (df.iloc[i]['close'] == recent_prices.min() and 
                      df.iloc[i]['volume'] < recent_volumes.mean() * 0.8):
                    df.iloc[i, df.columns.get_loc('vp_divergence')] = True
            
            return df
            
        except Exception as e:
            self._log(f"分析量价关系失败: {str(e)}", "error")
            return df
    
    def _generate_volume_price_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成量价信号"""
        try:
            min_obv_trend_periods = int(params.get("min_obv_trend_periods", 3))
            
            df['vp_signal'] = 'HOLD'
            df['vp_strength'] = 0.0
            df['vp_reason'] = 'no_signal'
            
            for i in range(min_obv_trend_periods, len(df)):
                current = df.iloc[i]
                
                if pd.isna(current['volume_ratio']):
                    continue
                
                # 放量上涨信号
                if current['vp_type'] == 'surge_up':
                    # OBV确认
                    obv_confirmed = current['obv_trend'] == 'up'
                    
                    if obv_confirmed:
                        strength = min(0.9, 0.6 + (current['volume_ratio'] - 2) * 0.1)
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'BUY'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = strength
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'surge_up_confirmed'
                    else:
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'BUY'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.5
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'surge_up'
                
                # 放量下跌信号
                elif current['vp_type'] == 'surge_down':
                    # OBV确认
                    obv_confirmed = current['obv_trend'] == 'down'
                    
                    if obv_confirmed:
                        strength = min(0.9, 0.6 + (current['volume_ratio'] - 2) * 0.1)
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'SELL'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = strength
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'surge_down_confirmed'
                    else:
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'SELL'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.5
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'surge_down'
                
                # 缩量上涨（弱势信号）
                elif current['vp_type'] == 'low_vol_up':
                    df.iloc[i, df.columns.get_loc('vp_signal')] = 'HOLD'
                    df.iloc[i, df.columns.get_loc('vp_strength')] = 0.3
                    df.iloc[i, df.columns.get_loc('vp_reason')] = 'low_vol_up_weak'
                
                # 量价背离警告
                elif current['vp_divergence']:
                    if current['price_trend'] == 'up':
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'SELL'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.6
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'volume_price_divergence_top'
                    else:
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'BUY'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.6
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'volume_price_divergence_bottom'
                
                # 放量滞涨/滞跌
                elif current['vp_type'] == 'high_vol_stagnant':
                    # 根据前期趋势判断
                    if current['price_trend'] == 'up':
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'SELL'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.4
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'high_vol_stagnant_top'
                    elif current['price_trend'] == 'down':
                        df.iloc[i, df.columns.get_loc('vp_signal')] = 'BUY'
                        df.iloc[i, df.columns.get_loc('vp_strength')] = 0.4
                        df.iloc[i, df.columns.get_loc('vp_reason')] = 'high_vol_stagnant_bottom'
            
            return df
            
        except Exception as e:
            self._log(f"生成量价信号失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            for symbol, df in data.items():
                if df.empty or len(df) < 20:
                    self._log(f"股票{symbol}数据不足，无法生成量价信号")
                    continue
                
                # 获取最近的量价信号
                recent_signals = df[df['vp_signal'] != 'HOLD'].tail(3)
                
                # 获取当前量价状态
                latest = df.iloc[-1]
                current_price = latest['close']
                current_volume_ratio = latest['volume_ratio']
                current_vp_type = latest['vp_type']
                
                if not recent_signals.empty:
                    # 处理交易信号
                    for _, row in recent_signals.iterrows():
                        direction = row['vp_signal']
                        confidence = row['vp_strength']
                        reason = row['vp_reason']
                        
                        # 构建触发条件描述
                        reason_map = {
                            'surge_up_confirmed': '放量上涨+OBV确认',
                            'surge_down_confirmed': '放量下跌+OBV确认',
                            'surge_up': '放量上涨',
                            'surge_down': '放量下跌',
                            'volume_price_divergence_top': '高位量价背离',
                            'volume_price_divergence_bottom': '低位量价背离',
                            'high_vol_stagnant_top': '高位放量滞涨',
                            'high_vol_stagnant_bottom': '低位放量不跌'
                        }
                        condition = reason_map.get(reason, '量价信号')
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction=direction,
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                volume_ratio=float(row['volume_ratio']) if pd.notna(row['volume_ratio']) else 0,
                                price_change_pct=float(row['price_change_pct']) if pd.notna(row['price_change_pct']) else 0,
                                vp_type=str(row['vp_type']),
                                obv_trend=str(row.get('obv_trend', 'unknown'))
                            )
                        )
                else:
                    # 生成状态信号
                    if pd.notna(current_volume_ratio):
                        if current_vp_type == 'surge_up':
                            condition = f"放量上涨_{current_volume_ratio:.1f}倍量"
                            confidence = 0.4
                        elif current_vp_type == 'surge_down':
                            condition = f"放量下跌_{current_volume_ratio:.1f}倍量"
                            confidence = 0.4
                        elif current_vp_type == 'low_vol_up':
                            condition = f"缩量上涨_{current_volume_ratio:.1f}倍量"
                            confidence = 0.3
                        else:
                            condition = f"量价中性_{current_volume_ratio:.1f}倍量"
                            confidence = 0.3
                        
                        signals.append(
                            self.create_signal(
                                symbol=symbol,
                                name=symbol,
                                direction="HOLD",
                                signal_type="technical",
                                confidence=confidence,
                                trigger_condition=condition,
                                current_price=float(current_price),
                                volume_ratio=float(current_volume_ratio),
                                vp_type=str(current_vp_type)
                            )
                        )
            
            self._log(f"共生成{len(signals)}个量价信号")
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            self._log(f"执行量价确认择时策略，模式: {context.mode}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"量价策略执行完成: 买入信号{buy_signals}个, 卖出信号{sell_signals}个, 观望信号{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []