{"template_id": "turnover_filter", "name": "换手率", "description": "筛选换手率满足要求的股票   <span style='color: #808080'>例：换手率大于10(%)。</span>", "version": "1.0.0", "author": "quantcard", "stars": 1, "tags": ["选股", "基础筛选", "实时"], "parameters": {"operator": {"type": "select", "label": "条件", "description": "换手率条件(%)", "required": true, "options": [{"label": "大于", "value": "大于"}, {"label": "小于", "value": "小于"}, {"label": "介于", "value": "介于"}], "default": "大于"}, "turnover1": {"type": "number", "label": "换手率条件1", "description": "换手率条件1(%)", "required": true, "default": 10, "min": 0, "max": 100, "unit": "(%)"}, "turnover2": {"type": "number", "label": "换手率条件2", "description": "换手率条件2(%)，仅在'介于'模式下使用", "required": false, "default": null, "min": 0, "max": 100, "unit": "(%)", "visibleWhen": {"parameter": "operator", "value": "介于"}}}, "parameterGroups": {"turnover_filter": {"parameters": ["operator", "turnover1", "turnover2"], "displayMode": "inline", "prefix": "换手率", "separator": "-", "layout": "horizontal"}}, "outputs": {"stocks": {"type": "array", "description": "筛选出的股票列表", "items": {"type": "object", "properties": {"代码": {"type": "string"}, "名称": {"type": "string"}, "换手率": {"type": "number"}}}}}, "ui": {"icon": "filter", "color": "#1890ff", "group": "筛选", "order": 1, "form": {"layout": "horizontal", "compact": true, "showDescription": false}}}