"""
API路由入口
提供统一的API路由注册和版本管理
"""
from fastapi import APIRouter
from .v1 import api_router as api_v1_router
from .v1.endpoints import card_system, inventory

# 创建主API路由器
api_router = APIRouter()

# 注册API v1版本路由
api_router.include_router(api_v1_router, prefix="/v1")

# 注册常用API的简化路径（不带版本号）
api_router.include_router(card_system.router, prefix="/cards", tags=["cards"])
api_router.include_router(inventory.router, prefix="/inventory", tags=["inventory"])