"""
内外比策略
基于内盘外盘比值的选股和择时策略
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import yaml
import json
from pathlib import Path

from app.core.strategy.base import UnifiedStrategyCard
from app.core.runtime.types import RuntimeContext, Signal, SignalType, StrategyMode
from app.services.realtime_quote_service import RealtimeQuoteService
from app.services.minute_kline_service import MinuteKlineService

logger = logging.getLogger(__name__)

# 从 template.json 加载配置
template_path = Path(__file__).parent / "template.json"
with open(template_path, "r", encoding="utf-8") as f:
    template = json.load(f)

class InnerOuterRatio(UnifiedStrategyCard):
    """内外比策略：基于内盘外盘比值进行选股和择时分析"""
    
    def __init__(self, context: Optional[RuntimeContext] = None):
        """初始化策略"""
        super().__init__(context)
        
        # 加载配置文件
        config_path = Path(__file__).parent / "config.yaml"
        with open(config_path, "r", encoding="utf-8") as f:
            self.config = yaml.safe_load(f)
        
        # 根据不同模式初始化不同的服务
        self.quote_service = RealtimeQuoteService.get_instance()
        self.kline_service = MinuteKlineService.get_instance()

    async def prepare_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备数据"""
        try:
            # 检查串行执行模式
            if not self._check_sequential_mode(context):
                return {}
            
            if context.mode == StrategyMode.FILTER:
                # 选股模式：使用实时报价数据
                return await self._prepare_filter_data(context)
            else:
                # 择时模式：使用分钟K线数据
                return await self._prepare_timing_data(context)
            
        except Exception as e:
            self._log(f"准备数据失败: {str(e)}", "error")
            return {}
    
    async def _prepare_filter_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备选股数据"""
        try:
            # 从context.data_cache获取实时报价数据
            quote_data = context.data_cache.get("realtime_quote_data", {})
            if not quote_data:
                self._log("未能从上下文中获取实时报价数据", "warning")
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in quote_data.items():
                if df.empty:
                    continue
                
                # 确保数据按时间排序
                df = df.sort_values(by='time').copy()
                
                # 计算内外比指标
                df = self._calculate_inner_outer_ratio(df, context.parameters)
                
                # 过滤有效数据
                df = self._filter_valid_data(df, context.parameters)
                
                if not df.empty:
                    processed_data[symbol] = df
                    
                    # 打印调试信息
                    latest = df.iloc[-1]
                    self._log(f"股票{symbol}最新内外比: {latest.get('outer_inner_ratio', 0):.3f}, "
                             f"内盘:{latest.get('inner_vol', 0)}, 外盘:{latest.get('outer_vol', 0)}")
            
            self._log(f"选股模式处理{len(processed_data)}只股票的内外比数据")
            return processed_data
            
        except Exception as e:
            self._log(f"准备选股数据失败: {str(e)}", "error")
            return {}
    
    async def _prepare_timing_data(self, context: RuntimeContext) -> Dict[str, pd.DataFrame]:
        """准备择时数据"""
        try:
            # 从context.data_cache获取分钟K线数据
            kline_data = context.data_cache.get("kline_data", {})
            if not kline_data:
                self._log("未能从上下文中获取分钟K线数据", "warning") 
                return {}
            
            # 处理每只股票的数据
            processed_data = {}
            for symbol, df in kline_data.items():
                if df.empty:
                    continue
                
                # 确保数据按时间排序
                df = df.sort_values(by='time').copy()
                
                # 计算内外比指标
                df = self._calculate_inner_outer_ratio(df, context.parameters)
                
                # 计算技术指标
                df = self._calculate_technical_indicators(df, context.parameters)
                
                # 生成交易信号
                df = self._generate_trading_signals(df, context.parameters)
                
                # 过滤有效数据
                df = self._filter_valid_data(df, context.parameters)
                
                if not df.empty:
                    processed_data[symbol] = df
                    
                    # 打印调试信息
                    recent_signals = df[df['io_signal'] != 'HOLD'].tail(3)
                    if not recent_signals.empty:
                        self._log(f"股票{symbol}近期内外比信号:")
                        for _, row in recent_signals.iterrows():
                            self._log(f"  {row['time']}: {row['io_signal']}, 比值{row['outer_inner_ratio']:.3f}")
            
            self._log(f"择时模式处理{len(processed_data)}只股票的内外比数据")
            return processed_data
            
        except Exception as e:
            self._log(f"准备择时数据失败: {str(e)}", "error")
            return {}
    
    def _calculate_inner_outer_ratio(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算内外比指标"""
        try:
            # 确保内外盘数据存在，如果不存在则尝试从成交量估算
            if 'inner_vol' not in df.columns or 'outer_vol' not in df.columns:
                # 如果没有内外盘数据，根据价格变动估算
                df['price_change'] = df['close'].pct_change() if 'close' in df.columns else 0
                df['inner_vol'] = np.where(df['price_change'] <= 0, df['volume'] * 0.6, df['volume'] * 0.4)
                df['outer_vol'] = df['volume'] - df['inner_vol']
            
            # 处理零值和空值
            df['inner_vol'] = df['inner_vol'].fillna(0)
            df['outer_vol'] = df['outer_vol'].fillna(0)
            
            # 计算外盘/内盘比值，避免除零
            df['outer_inner_ratio'] = np.where(
                df['inner_vol'] > 0,
                df['outer_vol'] / df['inner_vol'],
                np.where(df['outer_vol'] > 0, 10.0, 1.0)  # 如果内盘为0但外盘不为0，设为高比值
            )
            
            # 计算内外盘总成交量
            df['inner_outer_total'] = df['inner_vol'] + df['outer_vol']
            
            # 计算外盘占比
            df['outer_ratio'] = df['outer_vol'] / (df['inner_outer_total'] + 1)  # +1防止除零
            df['inner_ratio'] = df['inner_vol'] / (df['inner_outer_total'] + 1)
            
            # 平滑处理
            smooth_period = int(params.get("smooth_period", 5))
            if smooth_period > 1:
                df['outer_inner_ratio_smooth'] = df['outer_inner_ratio'].rolling(
                    window=smooth_period, min_periods=1
                ).mean()
                df['outer_ratio_smooth'] = df['outer_ratio'].rolling(
                    window=smooth_period, min_periods=1
                ).mean()
            else:
                df['outer_inner_ratio_smooth'] = df['outer_inner_ratio']
                df['outer_ratio_smooth'] = df['outer_ratio']
            
            return df
            
        except Exception as e:
            self._log(f"计算内外比指标失败: {str(e)}", "error")
            return df
    
    def _calculate_technical_indicators(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """计算技术指标（仅择时模式）"""
        try:
            # 计算内外比的移动平均
            df['ratio_ma5'] = df['outer_inner_ratio_smooth'].rolling(window=5, min_periods=1).mean()
            df['ratio_ma10'] = df['outer_inner_ratio_smooth'].rolling(window=10, min_periods=1).mean()
            
            # 计算内外比的标准差
            df['ratio_std'] = df['outer_inner_ratio_smooth'].rolling(window=10, min_periods=1).std()
            
            # 计算内外比的相对强弱指数（类RSI）
            df = self._calculate_ratio_rsi(df)
            
            # 计算资金流向强度
            df['money_flow_strength'] = (df['outer_ratio_smooth'] - 0.5) * 2  # 标准化到-1到1
            
            return df
            
        except Exception as e:
            self._log(f"计算技术指标失败: {str(e)}", "error")
            return df
    
    def _calculate_ratio_rsi(self, df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """计算内外比的RSI指标"""
        try:
            ratio_change = df['outer_inner_ratio_smooth'].diff()
            
            gain = ratio_change.where(ratio_change > 0, 0).rolling(window=period).mean()
            loss = -ratio_change.where(ratio_change < 0, 0).rolling(window=period).mean()
            
            rs = gain / (loss + 1e-8)  # 防止除零
            df['ratio_rsi'] = 100 - (100 / (1 + rs))
            df['ratio_rsi'] = df['ratio_rsi'].fillna(50)
            
            return df
            
        except Exception as e:
            self._log(f"计算内外比RSI失败: {str(e)}", "error")
            return df
    
    def _generate_trading_signals(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """生成交易信号"""
        try:
            ratio_high = float(params.get("ratio_threshold_high", 1.5))
            ratio_low = float(params.get("ratio_threshold_low", 0.7))
            
            df['io_signal'] = 'HOLD'
            df['io_strength'] = 0.5
            
            # 基于外内比阈值的信号
            buy_condition = (
                (df['outer_inner_ratio_smooth'] >= ratio_high) &
                (df['outer_ratio_smooth'] > 0.55) &  # 外盘占比超过55%
                (df['ratio_rsi'] > 50)  # RSI支持
            )
            
            sell_condition = (
                (df['outer_inner_ratio_smooth'] <= ratio_low) &
                (df['outer_ratio_smooth'] < 0.45) &  # 外盘占比低于45%
                (df['ratio_rsi'] < 50)  # RSI支持
            )
            
            # 生成信号
            df.loc[buy_condition, 'io_signal'] = 'BUY'
            df.loc[sell_condition, 'io_signal'] = 'SELL'
            
            # 计算信号强度
            df.loc[buy_condition, 'io_strength'] = np.minimum(0.9,
                0.5 + 0.3 * (df.loc[buy_condition, 'outer_inner_ratio_smooth'] - ratio_high) / ratio_high
            )
            
            df.loc[sell_condition, 'io_strength'] = np.minimum(0.9,
                0.5 + 0.3 * (ratio_low - df.loc[sell_condition, 'outer_inner_ratio_smooth']) / ratio_low
            )
            
            return df
            
        except Exception as e:
            self._log(f"生成交易信号失败: {str(e)}", "error")
            return df
    
    def _filter_valid_data(self, df: pd.DataFrame, params: Dict[str, Any]) -> pd.DataFrame:
        """过滤有效数据"""
        try:
            min_volume = int(params.get("min_volume_threshold", 1000000))
            min_inner_outer_sum = int(params.get("min_inner_outer_sum", 500000))
            
            # 过滤条件
            valid_condition = (
                (df['volume'] >= min_volume) &
                (df['inner_outer_total'] >= min_inner_outer_sum) &
                (df['outer_inner_ratio'] > 0) &
                (df['outer_inner_ratio'] < 100)  # 排除异常值
            )
            
            return df[valid_condition].copy()
            
        except Exception as e:
            self._log(f"过滤有效数据失败: {str(e)}", "error")
            return df
    
    async def generate_signal(self, data: Dict[str, pd.DataFrame], params: Dict[str, Any]) -> List[Signal]:
        """生成信号"""
        try:
            if not data:
                return []
            
            signals = []
            ratio_high = float(params.get("ratio_threshold_high", 1.5))
            ratio_low = float(params.get("ratio_threshold_low", 0.7))
            
            for symbol, df in data.items():
                if df.empty:
                    continue
                
                # 获取最新数据
                latest = df.iloc[-1]
                current_price = latest.get('close', latest.get('current_price', 0))
                ratio = latest['outer_inner_ratio_smooth']
                outer_ratio = latest['outer_ratio_smooth']
                
                # 判断信号类型
                if hasattr(latest, 'io_signal') and latest['io_signal'] != 'HOLD':
                    # 择时模式的交易信号
                    direction = latest['io_signal']
                    confidence = latest['io_strength']
                    
                    condition = f"内外比{direction}_{ratio:.3f}_外占比{outer_ratio:.3f}"
                    
                else:
                    # 选股模式或持有信号
                    if ratio >= ratio_high:
                        direction = "BUY"
                        confidence = min(0.8, 0.5 + 0.3 * (ratio - ratio_high) / ratio_high)
                        condition = f"外盘强势_比值{ratio:.3f}_外占比{outer_ratio:.3f}"
                        
                    elif ratio <= ratio_low:
                        direction = "SELL"
                        confidence = min(0.8, 0.5 + 0.3 * (ratio_low - ratio) / ratio_low)
                        condition = f"内盘强势_比值{ratio:.3f}_外占比{outer_ratio:.3f}"
                        
                    else:
                        direction = "HOLD"
                        confidence = 0.4
                        condition = f"内外均衡_比值{ratio:.3f}_外占比{outer_ratio:.3f}"
                
                # 构建信号
                signal_data = {
                    "symbol": symbol,
                    "name": symbol,
                    "direction": direction,
                    "signal_type": "technical",
                    "confidence": confidence,
                    "trigger_condition": condition,
                    "current_price": float(current_price),
                    "outer_inner_ratio": float(ratio),
                    "outer_ratio": float(outer_ratio),
                    "inner_vol": float(latest['inner_vol']),
                    "outer_vol": float(latest['outer_vol']),
                    "volume": float(latest['volume'])
                }
                
                # 添加择时模式特有的字段
                if hasattr(latest, 'ratio_rsi'):
                    signal_data.update({
                        "ratio_rsi": float(latest['ratio_rsi']),
                        "money_flow_strength": float(latest['money_flow_strength'])
                    })
                
                signals.append(self.create_signal(**signal_data))
            
            self._log(f"内外比策略共生成{len(signals)}个信号")
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            self._log(f"买入信号{buy_signals}个，卖出信号{sell_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"生成信号失败: {str(e)}", "error")
            return []
    
    async def execute(self, context: RuntimeContext) -> List[Signal]:
        """执行策略"""
        try:
            # 添加执行模式日志
            mode_name = "选股模式" if context.mode == StrategyMode.FILTER else "择时模式"
            self._log(f"执行内外比策略，模式: {mode_name}")
            
            # 准备数据
            data = await self.prepare_data(context)
            
            # 生成信号
            signals = await self.generate_signal(data, context.parameters)
            
            # 记录结果
            buy_signals = len([s for s in signals if s.direction == "BUY"])
            sell_signals = len([s for s in signals if s.direction == "SELL"])
            hold_signals = len([s for s in signals if s.direction == "HOLD"])
            
            self._log(f"内外比策略执行完成: 买入{buy_signals}个, 卖出{sell_signals}个, 持有{hold_signals}个")
            
            return signals
            
        except Exception as e:
            self._log(f"策略执行失败: {str(e)}", "error")
            return []